# Polish translation for Canvider
# Copyright (C) 2025 Canvider
# This file is distributed under the same license as the Canvider package.
#
msgid ""
msgstr ""
"Project-Id-Version: Canvider 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-26 23:14+0000\n"
"PO-Revision-Date: 2025-07-26 23:14+0000\n"
"Last-Translator: Canvider Team\n"
"Language-Team: Polish\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 "
"|| n%100>=20) ? 1 : 2);\n"

# Email subjects
#: feed/email_utils.py:202
#, python-format
msgid "Application Status Update - %(job_title)s"
msgstr "Aktualizacja Statusu Aplikacji - %(job_title)s"

#: feed/email_utils.py:247
#, python-format
msgid "Meeting Invitation - %(job_title)s"
msgstr "Zaproszenie na Spotkanie - %(job_title)s"

#: feed/email_utils.py:329
#, python-format
msgid "Application Received - %(job_title)s"
msgstr "Aplikacja Otrzymana - %(job_title)s"

#: feed/email_utils.py:376
#, python-format
msgid "Invitation: Join %(company_name)s at Canvider ATS"
msgstr "Zaproszenie: Dołącz do %(company_name)s w Canvider ATS"

#: feed/views.py:154
#, python-format
msgid "<strong>%(name)s</strong> applied for <strong>%(position)s</strong>"
msgstr ""
"<strong>%(name)s</strong> aplikował na stanowisko <strong>%(position)s</"
"strong>"

#: feed/views.py:195
#, python-format
msgid ""
"<strong>%(name)s</strong> moved to <strong>%(state)s</strong> for "
"<strong>%(position)s</strong>"
msgstr ""
"<strong>%(name)s</strong> przeniesiony do <strong>%(state)s</strong> dla "
"stanowiska <strong>%(position)s</strong>"

#: feed/views.py:225
#, python-format
msgid "A new vacancy <strong>%(vacancy_title)s</strong> is published"
msgstr "Nowa wakatcja <strong>%(vacancy_title)s</strong> została opublikowana"

#: feed/views.py:249
msgid "New comment on application of"
msgstr "Nowy komentarz do aplikacji"

#: feed/views.py:251
msgid "New comment on application ID"
msgstr "Nowy komentarz do ID aplikacji"

#: feed/views.py:267
msgid "now"
msgstr "teraz"

#: feed/views.py:270
msgid "1 minute ago"
msgstr "1 minutę temu"

#: feed/views.py:273
msgid "minutes ago"
msgstr "minut temu"

#: feed/views.py:276
msgid "1 hour ago"
msgstr "1 godzinę temu"

#: feed/views.py:280
msgid "hours ago"
msgstr "godzin temu"

#: feed/views.py:283
msgid "yesterday"
msgstr "wczoraj"

#: feed/views.py:287
msgid "days ago"
msgstr "dni temu"

#: feed/views.py:290
msgid "last week"
msgstr "w zeszłym tygodniu"

#: feed/views.py:294
msgid "weeks ago"
msgstr "tygodni temu"

#: feed/views.py:297
msgid "last month"
msgstr "w zeszłym miesiącu"

#: feed/views.py:301
msgid "months ago"
msgstr "miesięcy temu"

#: feed/views.py:306
msgid "last year"
msgstr "w zeszłym roku"

#: feed/views.py:308
msgid "years ago"
msgstr "lat temu"

#: feed/views.py:1045
msgid "Profile photo changed successfully!"
msgstr "Zdjęcie profilowe zostało zmienione!"

#: feed/views.py:1050
msgid "Please select a photo."
msgstr "Proszę wybrać zdjęcie."

#: feed/views.py:1876
#, python-format
msgid "Language changed to %(language)s"
msgstr "Język zmieniono na %(language)s"

#: feed/views.py:1880
msgid "Invalid language selection"
msgstr "Nieprawidłowy wybór języka"

#: feed/views.py:1909 templates/feed.html:17
msgid "Dashboard"
msgstr "Panel główny"

#: feed/views.py:2079
msgid "Invitation mail sent successfully!"
msgstr "Zaproszenie wysłano pomyślnie!"

#: feed/views.py:2081 feed/views.py:2086 feed/views.py:2229
msgid "Failed to send the invitation. Please check the form."
msgstr "Nie udało się wysłać zaproszenia. Proszę sprawdzić formularz."

#: feed/views.py:2113
msgid "Passwords do not match."
msgstr "Hasła nie pasują do siebie."

#: feed/views.py:2144
msgid "No employer found to associate with this account."
msgstr "Nie znaleziono pracodawcy do powiązania z tym kontem."

#: feed/views.py:2154
msgid "Registration completed successfully! You can now log in."
msgstr "Rejestracja zakończona pomyślnie! Możesz się teraz zalogować."

#: feed/views.py:2158
#, python-format
msgid "Error creating account: %(error)s"
msgstr "Błąd podczas tworzenia konta: %(error)s"

#: feed/views.py:2184
msgid "Access denied."
msgstr "Dostęp zabroniony."

#: feed/views.py:2211
msgid "Invitation sent successfully!"
msgstr "Zaproszenie wysłano pomyślnie!"

#: feed/views.py:2240
msgid "User removed successfully!"
msgstr "Użytkownik został usunięty!"

#: feed/views.py:2253
msgid "User status changed successfully!"
msgstr "Status użytkownika został zmieniony!"

#: feed/views.py:2405
msgid "Talent request sent successfully! Our team will get back to you soon."
msgstr "Prośba o talent wysłana! Nasz zespół skontaktuje się z Tobą wkrótce."

#: feed/views.py:2409
msgid "Invalid request method."
msgstr "Nieprawidłowa metoda żądania."

# Status update messages
#: feed/views.py:2462
msgid "Status updated and notification email sent to candidate."
msgstr "Status zaktualizowany i e-mail z powiadomieniem wysłany do kandydata."

#: feed/views.py:2464 feed/views.py:2468
msgid "Status updated but email notification failed to send."
msgstr ""
"Status zaktualizowany, ale nie udało się wysłać powiadomienia e-mailowego."

#: feed/views.py:3639
msgid ""
"Mail has been sent successfully, it will appear on this page after a few "
"minutes."
msgstr ""
"E-mail został wysłany pomyślnie, pojawi się na tej stronie za kilka minut."

#: feed/views.py:3641
msgid "Failed to send email. Please try again."
msgstr "Nie udało się wysłać e-maila. Proszę spróbować ponownie."

#: feed/views.py:3646
#, python-format
msgid "Failed to send email: %(error)s"
msgstr "Nie udało się wysłać e-maila: %(error)s"

#: feed/views.py:4604
msgid "Image URL and employer ID are required"
msgstr "Wymagane są URL obrazu i ID pracodawcy"

#: feed/views.py:4636
#, python-format
msgid "Failed to remove image: %(error)s"
msgstr "Nie udało się usunąć obrazu: %(error)s"

#: feed/views.py:4641
msgid "Invalid request method"
msgstr "Nieprawidłowa metoda żądania"

#: templates/applicant_dev.html:30
msgid "Applied for:"
msgstr "Aplikowano na:"

#: templates/applicant_dev.html:89 templates/applicant_dev.html:870
msgid "Schedule Interview"
msgstr "Zaplanuj rozmowę"

#: templates/applicant_dev.html:99
msgid "Change State"
msgstr "Zmień status"

#: templates/applicant_dev.html:114
msgid "Dashboard & AI"
msgstr "Panel i AI"

#: templates/applicant_dev.html:123
msgid "Candidate Background"
msgstr "Dane kandydata"

#: templates/applicant_dev.html:132 templates/applicant_dev.html:565
#: templates/applicant_dev.html:583
msgid "Resume"
msgstr "CV"

#: templates/applicant_dev.html:141
msgid "Journey"
msgstr "Historia"

#: templates/applicant_dev.html:150 templates/applicant_dev.html:785
msgid "Internal Comments"
msgstr "Komentarze wewnętrzne"

#: templates/applicant_dev.html:159 templates/applicant_dev.html:685
msgid "Emails"
msgstr "E-maile"

#: templates/applicant_dev.html:167 templates/applicant_dev.html:854
msgid "Job Details"
msgstr "Szczegóły stanowiska"

#: templates/applicant_dev.html:191
msgid "Profile Match Analysis"
msgstr "Analiza dopasowania profilu"

#: templates/applicant_dev.html:199
msgid "Key Highlights"
msgstr "Kluczowe elementy"

#: templates/applicant_dev.html:225
msgid "AI analysis will provide candidate highlights."
msgstr "Analiza AI przedstawi kluczowe informacje o kandydacie."

#: templates/applicant_dev.html:235
msgid "Areas for Improvement"
msgstr "Obszary do poprawy"

#: templates/applicant_dev.html:272
msgid "Candidate Summary"
msgstr "Podsumowanie kandydata"

#: templates/applicant_dev.html:321
msgid ""
"Based on the AI analysis, when the resume is compared to the job "
"requirements,"
msgstr "Na podstawie analizy AI, po porównaniu CV z wymaganiami stanowiska,"

#: templates/applicant_dev.html:323
msgid "This candidate is an"
msgstr "Ten kandydat to"

#: templates/applicant_dev.html:324
msgid "excellent match"
msgstr "doskonałe dopasowanie"

#: templates/applicant_dev.html:326 templates/applicant_dev.html:329
#: templates/applicant_dev.html:332
msgid "This candidate is a"
msgstr "Ten kandydat to"

#: templates/applicant_dev.html:327
msgid "good match"
msgstr "dobre dopasowanie"

#: templates/applicant_dev.html:330
msgid "fair match"
msgstr "wystarczające dopasowanie"

#: templates/applicant_dev.html:333
msgid "weak match"
msgstr "słabe dopasowanie"

#: templates/applicant_dev.html:336
msgid "Analyze the CV with AI to see match details."
msgstr "Przeanalizuj CV za pomocą AI, aby zobaczyć szczegóły dopasowania."

#: templates/applicant_dev.html:363
msgid "AI Analysis Available"
msgstr "Dostępna analiza AI"

#: templates/applicant_dev.html:366
msgid "Leverage AI to analyze this candidate's CV against the job description."
msgstr ""
"Wykorzystaj AI do analizy CV kandydata w odniesieniu do opisu stanowiska."

#: templates/applicant_dev.html:387
msgid "Analyze with CanviderAI"
msgstr "Analizuj za pomocą CanviderAI"

#: templates/applicant_dev.html:402
msgid "Analyzing CV..."
msgstr "Analizowanie CV..."

#: templates/applicant_dev.html:403
msgid "This may take a moment"
msgstr "To może chwilę potrwać"

#: templates/applicant_dev.html:428 templates/applicant_dev.html:449
msgid "Analysis complete"
msgstr "Analiza zakończona"

#: templates/applicant_dev.html:460
msgid "Candidate Facts"
msgstr "Fakty o kandydacie"

#: templates/applicant_dev.html:467
msgid "Applied Position"
msgstr "Stanowisko"

#: templates/applicant_dev.html:473
msgid "Candidate's Address"
msgstr "Adres kandydata"

#: templates/applicant_dev.html:476 templates/applicant_dev.html:499
#: templates/applicant_dev.html:506 templates/applicant_dev.html:529
#: templates/applicant_dev.html:536 templates/people.html:119
#: templates/people.html:126 templates/people.html:133
msgid "Not analyzed"
msgstr "Nie przeanalizowano"

#: templates/applicant_dev.html:480
#: templates/emails/application_confirmation.html:30
#: templates/emails/application_status_change.html:32
#: templates/emails/general_message.html:32 templates/people.html:61
msgid "Application Date"
msgstr "Data aplikacji"

#: templates/applicant_dev.html:486
msgid "Application Portal"
msgstr "Portal aplikacji"

#: templates/applicant_dev.html:496
msgid "Latest/Current Position"
msgstr "Ostatnie/obecne stanowisko"

#: templates/applicant_dev.html:503
msgid "Latest/Current Employer"
msgstr "Ostatni/obecny pracodawca"

#: templates/applicant_dev.html:510 templates/jobs.html:69
#: templates/manage_permissions.html:117 templates/manage_permissions.html:264
#: templates/people.html:41 templates/people.html:89
#: templates/published_job_details.html:279
msgid "Status"
msgstr "Status"

#: templates/applicant_dev.html:516
msgid "Application ID"
msgstr "ID aplikacji"

#: templates/applicant_dev.html:526
msgid "Total Experience"
msgstr "Doświadczenie ogółem"

#: templates/applicant_dev.html:533
msgid "Education Level"
msgstr "Poziom wykształcenia"

#: templates/applicant_dev.html:540
msgid "Notice Period"
msgstr "Okres wypowiedzenia"

#: templates/applicant_dev.html:546
msgid "Last Communication Date"
msgstr "Data ostatniej komunikacji"

#: templates/applicant_dev.html:551 templates/applicant_dev.html:722
msgid "No emails found."
msgstr "Brak e-maili."

#: templates/applicant_dev.html:570
msgid "Uploaded on"
msgstr "Przesłano dnia"

#: templates/applicant_dev.html:588
msgid "Open in New Tab"
msgstr "Otwórz w nowej karcie"

#: templates/applicant_dev.html:593
msgid "Download"
msgstr "Pobierz"

#: templates/applicant_dev.html:619
msgid "PDF Preview Not Available"
msgstr "Podgląd PDF niedostępny"

#: templates/applicant_dev.html:620
msgid ""
"Your browser doesn't support PDF preview. Please download the file to view "
"it."
msgstr ""
"Twoja przeglądarka nie obsługuje podglądu PDF. Pobierz plik, aby go zobaczyć."

#: templates/applicant_dev.html:627
msgid "Download PDF"
msgstr "Pobierz PDF"

#: templates/applicant_dev.html:644
msgid "Application Stages"
msgstr "Etapy aplikacji"

#: templates/applicant_dev.html:661
msgid "Started on:"
msgstr "Rozpoczęto:"

#: templates/applicant_dev.html:673
msgid "No stages available for this application."
msgstr "Brak dostępnych etapów dla tej aplikacji."

#: templates/applicant_dev.html:691
msgid "Email History"
msgstr "Historia e-maili"

#: templates/applicant_dev.html:701
msgid "From:"
msgstr "Od:"

#: templates/applicant_dev.html:702
msgid "To:"
msgstr "Do:"

#: templates/applicant_dev.html:705 templates/applicant_dev.html:1145
msgid "Subject:"
msgstr "Temat:"

#: templates/applicant_dev.html:729 templates/applicant_dev.html:775
msgid "Send Email"
msgstr "Wyślij e-mail"

#: templates/applicant_dev.html:744
msgid "Subject"
msgstr "Temat"

#: templates/applicant_dev.html:754 templates/published_job_details.html:443
msgid "Email Body"
msgstr "Treść e-maila"

#: templates/applicant_dev.html:797
msgid "Add a comment"
msgstr "Dodaj komentarz"

#: templates/applicant_dev.html:803
msgid "Add your comment here..."
msgstr "Dodaj swój komentarz tutaj..."

#: templates/applicant_dev.html:808
msgid "Post Comment"
msgstr "Opublikuj komentarz"

#: templates/applicant_dev.html:841
msgid "No comments yet. Be the first to comment!"
msgstr "Brak komentarzy. Bądź pierwszy!"

#: templates/applicant_dev.html:876 templates/feed.html:302
msgid "Event Title"
msgstr "Tytuł wydarzenia"

#: templates/applicant_dev.html:887 templates/feed.html:312
msgid "Event Type"
msgstr "Typ wydarzenia"

#: templates/applicant_dev.html:893 templates/feed.html:318
msgid "Select an event type"
msgstr "Wybierz typ wydarzenia"

#: templates/applicant_dev.html:901 templates/feed.html:326
#: templates/manage_permissions.html:49
msgid "Recruiters"
msgstr "Rekruterzy"

#: templates/applicant_dev.html:906 templates/feed.html:331
msgid "Select one or many recruiters"
msgstr "Wybierz jednego lub wielu rekruterów"

#: templates/applicant_dev.html:916
#: templates/emails/application_confirmation.html:26
#: templates/emails/application_status_change.html:20
#: templates/emails/general_message.html:27
#: templates/emails/meeting_invitation.html:31 templates/feed.html:384
#: templates/people.html:31 templates/people.html:88 templates/profile.html:74
#: templates/profile.html:115
msgid "Position"
msgstr "Stanowisko"

#: templates/applicant_dev.html:933 templates/feed.html:406
msgid "Candidate"
msgstr "Kandydat"

#: templates/applicant_dev.html:949
msgid "Date"
msgstr "Data"

#: templates/applicant_dev.html:958 templates/feed.html:420
msgid "Start Time"
msgstr "Czas rozpoczęcia"

#: templates/applicant_dev.html:964 templates/feed.html:425
msgid "End Time"
msgstr "Czas zakończenia"

#: templates/applicant_dev.html:971 templates/emails/meeting_invitation.html:78
#: templates/feed.html:431
msgid "Meeting Link"
msgstr "Link do spotkania"

#: templates/applicant_dev.html:984 templates/feed.html:445
msgid "Generate Mirotalk Link"
msgstr "Generuj link Mirotalk"

#: templates/applicant_dev.html:999 templates/feed.html:460
msgid "Inform invitees by E-mail"
msgstr "Powiadom zaproszonych e-mailem"

#: templates/applicant_dev.html:1004 templates/feed.html:465
msgid "Color"
msgstr "Kolor"

#: templates/applicant_dev.html:1006 templates/feed.html:467
msgid "Blue"
msgstr "Niebieski"

#: templates/applicant_dev.html:1007 templates/feed.html:468
msgid "Light Blue"
msgstr "Jasnoniebieski"

#: templates/applicant_dev.html:1008 templates/feed.html:469
msgid "Purple"
msgstr "Fioletowy"

#: templates/applicant_dev.html:1009 templates/feed.html:470
msgid "Pink"
msgstr "Różowy"

#: templates/applicant_dev.html:1016 templates/applicant_dev.html:1173
#: templates/create_job_template.html:205 templates/feed.html:476
#: templates/job_details.html:134 templates/manage_permissions.html:413
#: templates/manage_permissions.html:463 templates/profile.html:125
#: templates/profile.html:152 templates/profile.html:184
#: templates/published_job_details.html:380
#: templates/published_job_details.html:465
#: templates/published_job_details.html:686
msgid "Cancel"
msgstr "Anuluj"

#: templates/applicant_dev.html:1019 templates/feed.html:479
msgid "Save Event"
msgstr "Zapisz wydarzenie"

#: templates/applicant_dev.html:1078
msgid "Change Application Status"
msgstr "Zmień status aplikacji"

#: templates/applicant_dev.html:1096
msgid "New Status"
msgstr "Nowy status"

#: templates/applicant_dev.html:1098
#: templates/emails/application_confirmation.html:37
msgid "New"
msgstr ""

#: templates/applicant_dev.html:1099
msgid "Review #1"
msgstr ""

#: templates/applicant_dev.html:1100
msgid "Review #2"
msgstr ""

#: templates/applicant_dev.html:1101
msgid "Review #3"
msgstr ""

#: templates/applicant_dev.html:1102
msgid "Review #4"
msgstr ""

#: templates/applicant_dev.html:1103
msgid "Review #5"
msgstr ""

#: templates/applicant_dev.html:1104
msgid "Ready for Decision"
msgstr ""

#: templates/applicant_dev.html:1105
msgid "Eliminated"
msgstr ""

#: templates/applicant_dev.html:1106
msgid "Offer Made"
msgstr ""

#: templates/applicant_dev.html:1107
msgid "Candidate Accepted"
msgstr ""

#: templates/applicant_dev.html:1108
msgid "Candidate Rejected"
msgstr ""

#: templates/applicant_dev.html:1112 templates/published_job_details.html:432
msgid "Internal Notes"
msgstr "Notatki wewnętrzne"

#: templates/applicant_dev.html:1112 templates/published_job_details.html:432
msgid "(visible only to recruiters)"
msgstr "(widoczne tylko dla rekruterów)"

#: templates/applicant_dev.html:1126
msgid "Notify candidate about this status change via email"
msgstr "Powiadom kandydata o zmianie statusu e-mailem"

#: templates/applicant_dev.html:1132
msgid "Email Message"
msgstr "Wiadomość e-mail"

#: templates/applicant_dev.html:1132
msgid "(will be included in the email to the candidate)"
msgstr "(zostanie dołączone do e-maila do kandydata)"

#: templates/applicant_dev.html:1143
msgid "Email Preview"
msgstr "Podgląd e-maila"

#: templates/applicant_dev.html:1145
msgid "Your application status has been updated"
msgstr "Status Twojej aplikacji został zaktualizowany"

#: templates/applicant_dev.html:1175
msgid "Save Change"
msgstr "Zapisz zmianę"

#: templates/careers_page.html:10
msgid "Career Page Setup"
msgstr "Konfiguracja strony kariery"

#: templates/careers_page.html:11
msgid "Choose the integration method that best suits your needs"
msgstr "Wybierz metodę integracji, która najlepiej odpowiada Twoim potrzebom"

#: templates/careers_page.html:23
msgid "RSS Feed Integration"
msgstr "Integracja kanału RSS"

#: templates/careers_page.html:24
msgid ""
"Already have your own careers page? Get our RSS feed to sync job listings"
msgstr ""
"Masz już własną stronę karier? Uzyskaj nasz kanał RSS do synchronizacji "
"ofert pracy"

#: templates/careers_page.html:26
msgid "Quick Setup"
msgstr "Szybka konfiguracja"

#: templates/careers_page.html:27
msgid "Auto Sync"
msgstr "Automatyczna synchronizacja"

#: templates/careers_page.html:32
msgid "Get RSS Feed"
msgstr "Uzyskaj kanał RSS"

#: templates/careers_page.html:40
msgid "Recommended"
msgstr "Rekomendowane"

#: templates/careers_page.html:45
msgid "Full HTML Page"
msgstr "Pełna strona HTML"

#: templates/careers_page.html:46
msgid "Let us manage your entire careers page with our professional template"
msgstr ""
"Pozwól nam zarządzać całą Twoją stroną karier przy użyciu naszego "
"profesjonalnego szablonu"

#: templates/careers_page.html:48
msgid "Professional"
msgstr "Profesjonalny"

#: templates/careers_page.html:49
msgid "Customizable"
msgstr "Modyfikowalny"

#: templates/careers_page.html:54
msgid "Create Page"
msgstr "Utwórz stronę"

#: templates/careers_page.html:67
msgid "Workloupe Platform"
msgstr "Platforma Workloupe"

#: templates/careers_page.html:68
msgid "Use our platform as your company's career page"
msgstr "Użyj naszej platformy jako strony karier Twojej firmy"

#: templates/careers_page.html:70
msgid "Hosted"
msgstr "Hostowane"

#: templates/careers_page.html:71
msgid "Full Featured"
msgstr "Pełna funkcjonalność"

#: templates/careers_page.html:76
msgid "Setup Platform"
msgstr "Skonfiguruj platformę"

#: templates/careers_page.html:91
msgid "Your RSS Feed URL"
msgstr "Twój adres URL kanału RSS"

#: templates/careers_page.html:98
msgid ""
"Use this RSS feed URL to automatically sync your job listings with your "
"existing careers page."
msgstr ""
"Użyj tego adresu URL kanału RSS, aby automatycznie synchronizować Twoje "
"oferty pracy z istniejącą stroną karier."

#: templates/careers_page.html:101
msgid "RSS Feed URL"
msgstr "Adres URL kanału RSS"

#: templates/careers_page.html:106 templates/published_job_details.html:516
#: templates/published_job_details.html:559
#: templates/published_job_details.html:585
#: templates/workloupe_platform.html:311
msgid "Copy"
msgstr "Kopiuj"

#: templates/careers_page.html:111 templates/create_careers_widget.html:182
msgid "Integration Instructions:"
msgstr "Instrukcje integracji:"

#: templates/careers_page.html:113
msgid "Copy the RSS feed URL above"
msgstr "Skopiuj powyższy adres URL kanału RSS"

#: templates/careers_page.html:114
msgid "Add it to your website's RSS feed reader or job board integration"
msgstr ""
"Dodaj go do czytnika RSS swojej strony lub integracji z tablicą ofert pracy"

#: templates/careers_page.html:115
msgid "Your job listings will automatically sync"
msgstr "Twoje oferty pracy będą automatycznie synchronizowane"

#: templates/careers_page.html:286 templates/create_careers_widget.html:716
#: templates/wordpress_integration.html:775
#: templates/workloupe_platform.html:1142
msgid "Copied!"
msgstr "Skopiowano!"

#: templates/careers_page.html:297 templates/create_careers_widget.html:726
#: templates/wordpress_integration.html:785
msgid "Failed to copy. Please copy manually."
msgstr "Nie udało się skopiować. Proszę skopiować ręcznie."

#: templates/create_careers_widget.html:13
msgid "Widget Builder"
msgstr "Konstruktor widżetu"

#: templates/create_careers_widget.html:14
msgid "Customize your careers widget"
msgstr "Dostosuj swój widżet kariery"

#: templates/create_careers_widget.html:22
#: templates/wordpress_integration.html:45
msgid "Company Branding"
msgstr "Branding firmy"

#: templates/create_careers_widget.html:26
#: templates/wordpress_integration.html:49 templates/workloupe_platform.html:34
msgid "Company Name"
msgstr "Nazwa firmy"

#: templates/create_careers_widget.html:27
#: templates/wordpress_integration.html:50
msgid "Enter company name"
msgstr "Wpisz nazwę firmy"

#: templates/create_careers_widget.html:31
#: templates/wordpress_integration.html:54
msgid "Tagline"
msgstr "Slogan"

#: templates/create_careers_widget.html:32
#: templates/wordpress_integration.html:55
msgid "Enter company tagline"
msgstr "Wpisz slogan firmy"

#: templates/create_careers_widget.html:36
#: templates/workloupe_platform.html:175
msgid "Company Logo"
msgstr "Logo firmy"

#: templates/create_careers_widget.html:38
msgid "Recommended: 200x80px, PNG or JPG"
msgstr "Rekomendowane: 200x80px, PNG lub JPG"

#: templates/create_careers_widget.html:46
msgid "Design & Colors"
msgstr "Projekt i kolory"

#: templates/create_careers_widget.html:50
#: templates/wordpress_integration.html:67
msgid "Primary Color"
msgstr "Kolor podstawowy"

#: templates/create_careers_widget.html:55
msgid "Background Color"
msgstr "Kolor tła"

#: templates/create_careers_widget.html:60
msgid "Text Color"
msgstr "Kolor tekstu"

#: templates/create_careers_widget.html:65
msgid "Widget Style"
msgstr "Styl widżetu"

#: templates/create_careers_widget.html:67
#: templates/wordpress_integration.html:75
msgid "Modern"
msgstr "Nowoczesny"

#: templates/create_careers_widget.html:68
#: templates/wordpress_integration.html:76
msgid "Classic"
msgstr "Klasyczny"

#: templates/create_careers_widget.html:69
#: templates/wordpress_integration.html:77
msgid "Minimal"
msgstr "Minimalistyczny"

#: templates/create_careers_widget.html:78
#: templates/wordpress_integration.html:93
msgid "Content Settings"
msgstr "Ustawienia treści"

#: templates/create_careers_widget.html:82
msgid "Max Jobs to Display"
msgstr "Maks. ofert do wyświetlenia"

#: templates/create_careers_widget.html:84
#: templates/create_careers_widget.html:85
#: templates/create_careers_widget.html:86 templates/jobs.html:186
#: templates/wordpress_integration.html:99
#: templates/wordpress_integration.html:100
#: templates/wordpress_integration.html:101
msgid "jobs"
msgstr "ofert"

#: templates/create_careers_widget.html:87
#: templates/wordpress_integration.html:102
msgid "All jobs"
msgstr "Wszystkie oferty"

#: templates/create_careers_widget.html:94
msgid "Show Salary Information"
msgstr "Pokaż informacje o wynagrodzeniu"

#: templates/create_careers_widget.html:101
msgid "Show Job Location"
msgstr "Pokaż lokalizację pracy"

#: templates/create_careers_widget.html:108
msgid "Show Posted Date"
msgstr "Pokaż datę publikacji"

#: templates/create_careers_widget.html:117
msgid "Generate Widget Code"
msgstr "Generuj kod widżetu"

#: templates/create_careers_widget.html:126
msgid "Live Preview"
msgstr "Podgląd na żywo"

#: templates/create_careers_widget.html:160
msgid "Your Widget Code"
msgstr "Twój kod widżetu"

#: templates/create_careers_widget.html:167
msgid ""
"Copy this code and paste it into your website where you want the careers "
"widget to appear."
msgstr ""
"Skopiuj ten kod i wklej go na swojej stronie, w miejscu, gdzie ma się "
"pojawić widżet kariery."

#: templates/create_careers_widget.html:172
msgid "HTML Widget Code"
msgstr "Kod HTML widżetu"

#: templates/create_careers_widget.html:175
#: templates/wordpress_integration.html:246
msgid "Copy Code"
msgstr "Kopiuj kod"

#: templates/create_careers_widget.html:184
msgid "Copy the HTML code above"
msgstr "Skopiuj powyższy kod HTML"

#: templates/create_careers_widget.html:185
msgid "Paste it into your website's HTML where you want the widget to appear"
msgstr ""
"Wklej go w kodzie HTML swojej strony, w miejscu, gdzie ma się pojawić widżet"

#: templates/create_careers_widget.html:186
msgid "The widget will automatically load your latest job postings"
msgstr "Widżet automatycznie załaduje Twoje najnowsze oferty pracy"

#: templates/create_careers_widget.html:187
msgid "The widget is responsive and will adapt to your website's layout"
msgstr "Widżet jest responsywny i dostosuje się do układu Twojej strony"

#: templates/create_careers_widget.html:192
#: templates/wordpress_integration.html:253
#: templates/workloupe_platform.html:338
msgid "Close"
msgstr "Zamknij"

#: templates/create_careers_widget.html:195
msgid "Download as HTML File"
msgstr "Pobierz jako plik HTML"

#: templates/create_careers_widget.html:802
msgid ""
"Widget package downloaded! Extract and follow the README instructions for "
"integration."
msgstr ""
"Pakiet widżetu pobrany! Wypakuj i postępuj zgodnie z instrukcjami README do "
"integracji."

#: templates/create_careers_widget.html:823
msgid "Widget file downloaded! Copy the code and paste it into your website."
msgstr "Plik widżetu pobrany! Skopiuj kod i wklej go na swoją stronę."

#: templates/create_job.html:3
msgid "Create Job Position"
msgstr "Utwórz stanowisko pracy"

#: templates/create_job.html:8
msgid "Basic Information"
msgstr "Informacje podstawowe"

#: templates/create_job.html:10 templates/job_details.html:1072
#: templates/job_preview_publish.html:590
msgid "Role Title"
msgstr "Tytuł roli"

#: templates/create_job.html:14
msgid "e.g. Senior Software Engineer"
msgstr "np. Starszy Inżynier Oprogramowania"

#: templates/create_job.html:18 templates/job_details.html:1076
#: templates/job_preview_publish.html:594
msgid "Office Location"
msgstr "Lokalizacja biura"

#: templates/create_job.html:21
msgid "Select office location"
msgstr "Wybierz lokalizację biura"

#: templates/create_job.html:31
msgid "No office locations found. Please"
msgstr "Nie znaleziono lokalizacji biura. Proszę"

#: templates/create_job.html:32
msgid "add office locations"
msgstr "dodać lokalizacje biur"

#: templates/create_job.html:32 templates/create_job.html:53
#: templates/create_job.html:74 templates/create_job.html:95
msgid "in your preferences first."
msgstr "w swoich preferencjach."

#: templates/create_job.html:36
msgid "No locations available"
msgstr "Brak dostępnych lokalizacji"

#: templates/create_job.html:41 templates/job_details.html:1080
#: templates/job_preview_publish.html:598
msgid "Work Schedule"
msgstr "Harmonogram pracy"

#: templates/create_job.html:44 templates/create_job.html:65
msgid "Select an option"
msgstr "Wybierz opcję"

#: templates/create_job.html:52
msgid "No work schedules found. Please"
msgstr "Nie znaleziono harmonogramów pracy. Proszę"

#: templates/create_job.html:53
msgid "add work schedules"
msgstr "dodać harmonogramy pracy"

#: templates/create_job.html:57
msgid "No work schedules available"
msgstr "Brak dostępnych harmonogramów pracy"

#: templates/create_job.html:62 templates/job_details.html:1084
#: templates/job_preview_publish.html:604
msgid "Office Schedule"
msgstr "Harmonogram biura"

#: templates/create_job.html:73
msgid "No office schedules found. Please"
msgstr "Nie znaleziono harmonogramów biura. Proszę"

#: templates/create_job.html:74
msgid "add office schedules"
msgstr "dodać harmonogramy biura"

#: templates/create_job.html:78
msgid "No office schedules available"
msgstr "Brak dostępnych harmonogramów biura"

#: templates/create_job.html:83 templates/job_details.html:1088
#: templates/job_preview_publish.html:610 templates/jobs.html:59
#: templates/profile.html:119
msgid "Department"
msgstr "Dział"

#: templates/create_job.html:86
msgid "Select a department"
msgstr "Wybierz dział"

#: templates/create_job.html:94
msgid "No departments found. Please"
msgstr "Nie znaleziono działów. Proszę"

#: templates/create_job.html:95
msgid "add departments"
msgstr "dodaj działy"

#: templates/create_job.html:99
msgid "No departments available"
msgstr "Brak dostępnych działów"

#: templates/create_job.html:107
msgid "Skills Requirements"
msgstr "Wymagane umiejętności"

#: templates/create_job.html:109
msgid "Skill"
msgstr "Umiejętność"

#: templates/create_job.html:114
msgid "e.g. JavaScript"
msgstr "np. JavaScript"

#: templates/create_job.html:116 templates/create_job.html:190
msgid "Add"
msgstr "Dodaj"

#: templates/create_job.html:119
msgid "Choose Skills"
msgstr "Wybierz umiejętności"

#: templates/create_job.html:133
msgid "Selected Skills"
msgstr "Wybrane umiejętności"

#: templates/create_job.html:136
msgid "No skills selected yet"
msgstr "Brak wybranych umiejętności"

#: templates/create_job.html:146
msgid "Salary Details (Optional)"
msgstr "Szczegóły wynagrodzenia (opcjonalnie)"

#: templates/create_job.html:149
msgid "Minimum Salary"
msgstr "Minimalne wynagrodzenie"

#: templates/create_job.html:153
msgid "Enter minimum salary"
msgstr "Wpisz minimalne wynagrodzenie"

#: templates/create_job.html:157
msgid "Maximum Salary"
msgstr "Maksymalne wynagrodzenie"

#: templates/create_job.html:161
msgid "Enter maximum salary"
msgstr "Wpisz maksymalne wynagrodzenie"

#: templates/create_job.html:165
msgid "Currency"
msgstr "Waluta"

#: templates/create_job.html:167
msgid "Select currency"
msgstr "Wybierz walutę"

#: templates/create_job.html:183
msgid "Benefits and Highlights (Optional)"
msgstr "Benefity i atuty (opcjonalnie)"

#: templates/create_job.html:188
msgid "e.g. Yearly Bonuses"
msgstr "np. Roczny bonus"

#: templates/create_job.html:193
msgid "Choose Benefits"
msgstr "Wybierz benefity"

#: templates/create_job.html:196
msgid "Dental Coverage"
msgstr "Opieka dentystyczna"

#: templates/create_job.html:199
msgid "Private Health Coverage"
msgstr "Prywatna opieka zdrowotna"

#: templates/create_job.html:202
msgid "Gym membership"
msgstr "Karnet na siłownię"

#: templates/create_job.html:205
msgid "Sign-in Bonus"
msgstr "Bonus za dołączenie"

#: templates/create_job.html:208
msgid "Relocation Package"
msgstr "Pakiet relokacyjny"

#: templates/create_job.html:211
msgid "Company Vehicle"
msgstr "Samochód służbowy"

#: templates/create_job.html:213
msgid "Food Card"
msgstr "Karta żywnościowa"

#: templates/create_job.html:215
msgid "Snacks & Coffee"
msgstr "Przekąski i kawa"

#: templates/create_job.html:218
msgid "Pet Friendly Office"
msgstr "Biuro przyjazne zwierzętom"

#: templates/create_job.html:222
msgid "Selected Benefits & Highlights"
msgstr "Wybrane benefity i atuty"

#: templates/create_job.html:225
msgid "No benefits or highlights selected yet"
msgstr "Brak wybranych benefitów lub atutów"

#: templates/create_job.html:232 templates/job_details.html:107
#: templates/job_preview_publish.html:159
msgid "Discard"
msgstr "Odrzuć"

#: templates/create_job.html:233
msgid "Next"
msgstr "Dalej"

#: templates/create_job_template.html:13 templates/create_job_template.html:19
#: templates/settings.html:41
msgid "Templates"
msgstr "Szablony"

#: templates/create_job_template.html:14
msgid "Create and manage reusable job description templates"
msgstr "Twórz i zarządzaj szablonami opisów stanowisk"

#: templates/create_job_template.html:17 templates/job_preferences.html:15
#: templates/manage_permissions.html:15 templates/navbar.html:104
#: templates/settings.html:9
msgid "Settings"
msgstr "Ustawienia"

#: templates/create_job_template.html:31
msgid "Total Templates"
msgstr "Wszystkie szablony"

#: templates/create_job_template.html:41
msgid "Created This Month"
msgstr "Utworzone w tym miesiącu"

#: templates/create_job_template.html:51
msgid "Jobs Created from Templates"
msgstr "Oferty utworzone z szablonów"

#: templates/create_job_template.html:61
msgid "Time Saved Using Templates"
msgstr "Czas zaoszczędzony dzięki szablonom"

#: templates/create_job_template.html:71
msgid "My Templates"
msgstr "Moje szablony"

#: templates/create_job_template.html:74 templates/create_job_template.html:105
#: templates/create_job_template.html:118
msgid "New Template"
msgstr "Nowy szablon"

#: templates/create_job_template.html:80
msgid "Search templates..."
msgstr "Wyszukaj szablony..."

#: templates/create_job_template.html:91
msgid "Updated"
msgstr "Zaktualizowano"

#: templates/create_job_template.html:94
msgid "Used 1 time"
msgstr "Użyty 1 raz"

#: templates/create_job_template.html:96
msgid "Used"
msgstr "Użyty"

#: templates/create_job_template.html:96
msgid "times"
msgstr "razy"

#: templates/create_job_template.html:107
msgid "Not saved yet"
msgstr "Jeszcze niezapisany"

#: templates/create_job_template.html:108
msgid "Not used yet"
msgstr "Jeszcze nieużywany"

#: templates/create_job_template.html:118
msgid "Enter template title"
msgstr "Wpisz tytuł szablonu"

#: templates/create_job_template.html:121
#: templates/create_job_template.html:196
msgid "Delete Template"
msgstr "Usuń szablon"

#: templates/create_job_template.html:126 templates/job_details.html:135
msgid "Save Template"
msgstr "Zapisz szablon"

#: templates/create_job_template.html:136
msgid "Heading 1"
msgstr "Nagłówek 1"

#: templates/create_job_template.html:137
msgid "Heading 2"
msgstr "Nagłówek 2"

#: templates/create_job_template.html:138
msgid "Heading 3"
msgstr "Nagłówek 3"

#: templates/create_job_template.html:139
msgid "Paragraph"
msgstr "Akapit"

#: templates/create_job_template.html:143
msgid "Bold"
msgstr "Pogrubienie"

#: templates/create_job_template.html:144
msgid "Italic"
msgstr "Kursywa"

#: templates/create_job_template.html:145
msgid "Underline"
msgstr "Podkreślenie"

#: templates/create_job_template.html:149
msgid "Bullet List"
msgstr "Lista punktowana"

#: templates/create_job_template.html:150
msgid "Numbered List"
msgstr "Lista numerowana"

#: templates/create_job_template.html:179
msgid "Enter your template content here..."
msgstr "Wpisz treść szablonu tutaj..."

#: templates/create_job_template.html:185 templates/job_details.html:100
msgid "characters"
msgstr "znaków"

#: templates/create_job_template.html:201
msgid "Are you sure you want to delete the"
msgstr "Czy na pewno chcesz usunąć"

#: templates/create_job_template.html:201
msgid "template? This action cannot be undone."
msgstr "szablon? Tej czynności nie można cofnąć."

#: templates/create_job_template.html:206 templates/job_preferences.html:70
msgid "Delete"
msgstr "Usuń"

# Application Confirmation Email
#: templates/emails/application_confirmation.html:4
msgid "Application Received"
msgstr "Aplikacja Otrzymana"

#: templates/emails/application_confirmation.html:6
msgid "Application Confirmation"
msgstr "Potwierdzenie Aplikacji"

#: templates/emails/application_confirmation.html:11
msgid "Thank you for your application!"
msgstr "Dziękujemy za Twoją aplikację!"

#: templates/emails/application_confirmation.html:14
#, python-format
msgid ""
"\n"
"        We have successfully received your application for the position of "
"<strong>%(job_title)s</strong>.\n"
"        "
msgstr ""
"\n"
"        Pomyślnie otrzymaliśmy Twoją aplikację na stanowisko "
"<strong>%(job_title)s</strong>.\n"
"        "

#: templates/emails/application_confirmation.html:22
msgid "Application Summary"
msgstr "Podsumowanie Aplikacji"

# msgid "Position"
# msgstr "Stanowisko"
#: templates/emails/application_confirmation.html:34
#: templates/emails/application_status_change.html:24
msgid "Application Status"
msgstr "Status Aplikacji"

#: templates/emails/application_confirmation.html:43
msgid "Application Source"
msgstr "Źródło Aplikacji"

#: templates/emails/application_confirmation.html:51
msgid "What Happens Next?"
msgstr "Co Dzieje Się Dalej?"

#: templates/emails/application_confirmation.html:53
msgid "Our recruitment team will review your application"
msgstr "Nasz zespół rekrutacyjny przejrzy Twoją aplikację"

#: templates/emails/application_confirmation.html:54
msgid ""
"If your profile matches our requirements, we will contact you for the next "
"steps"
msgstr ""
"Jeśli Twój profil pasuje do naszych wymagań, skontaktujemy się z Tobą w "
"sprawie kolejnych kroków"

#: templates/emails/application_confirmation.html:55
msgid ""
"You will receive email updates about any changes to your application status"
msgstr ""
"Otrzymasz aktualizacje e-mailowe o wszelkich zmianach statusu Twojej "
"aplikacji"

#: templates/emails/application_confirmation.html:56
msgid "The review process typically takes 5-10 business days"
msgstr "Proces przeglądu zazwyczaj trwa 5-10 dni roboczych"

#: templates/emails/application_confirmation.html:61
msgid "Stay Updated"
msgstr "Bądź na Bieżąco"

#: templates/emails/application_confirmation.html:63
msgid ""
"We will keep you informed throughout the selection process via email. Please "
"ensure that emails from our domain are not filtered as spam."
msgstr ""
"Będziemy informować Cię przez cały proces selekcji za pośrednictwem e-maila. "
"Proszę upewnić się, że e-maile z naszej domeny nie są filtrowane jako spam."

#: templates/emails/application_confirmation.html:66
msgid ""
"If you have any questions about your application or the position, please "
"don't hesitate to contact us using the reference ID provided at the bottom "
"of this email."
msgstr ""
"Jeśli masz jakiekolwiek pytania dotyczące Twojej aplikacji lub stanowiska, "
"nie wahaj się skontaktować z nami, używając ID referencyjnego podanego na "
"dole tego e-maila."

#: templates/emails/application_confirmation.html:71
msgid "About Our Company"
msgstr "O Naszej Firmie"

#: templates/emails/application_confirmation.html:73
msgid ""
"We appreciate your interest in joining our team. We are committed to "
"creating an inclusive and diverse workplace where everyone can thrive."
msgstr ""
"Doceniamy Twoje zainteresowanie dołączeniem do naszego zespołu. Jesteśmy "
"zobowiązani do tworzenia inkluzywnego i różnorodnego miejsca pracy, gdzie "
"każdy może się rozwijać."

#: templates/emails/application_confirmation.html:79
msgid ""
"Thank you for considering us as your next career opportunity. We look "
"forward to potentially working with you!"
msgstr ""
"Dziękujemy za rozważenie nas jako Twojej następnej możliwości kariery. "
"Cieszymy się na potencjalną współpracę z Tobą!"

# Application Status Update Email
#: templates/emails/application_status_change.html:4
#: templates/emails/application_status_change.html:6
msgid "Application Status Update"
msgstr "Aktualizacja Statusu Aplikacji"

#: templates/emails/application_status_change.html:11
#, python-format
msgid ""
"\n"
"        We would like to inform you that your application for the position "
"of <strong>%(job_title)s</strong> has been updated.\n"
"        "
msgstr ""
"\n"
"        Chcielibyśmy poinformować, że Twoja aplikacja na stanowisko "
"<strong>%(job_title)s</strong> została zaktualizowana.\n"
"        "

# msgid "Application Date"
# msgstr "Data Aplikacji"
#: templates/emails/application_status_change.html:37
msgid "Last Updated"
msgstr "Ostatnia Aktualizacja"

#: templates/emails/application_status_change.html:46
msgid "Additional Information"
msgstr "Dodatkowe Informacje"

#: templates/emails/application_status_change.html:54
msgid "Congratulations! We are excited to welcome you to our team."
msgstr "Gratulacje! Cieszymy się, że możemy powitać Cię w naszym zespole."

#: templates/emails/application_status_change.html:57
msgid ""
"Our HR team will be in touch with you shortly regarding the next steps, "
"including onboarding information and your start date."
msgstr ""
"Nasz zespół HR skontaktuje się z Tobą wkrótce w sprawie kolejnych kroków, w "
"tym informacji o wdrożeniu i dacie rozpoczęcia pracy."

#: templates/emails/application_status_change.html:63
msgid ""
"Your application is currently under review. We will keep you updated on any "
"developments."
msgstr ""
"Twoja aplikacja jest obecnie w trakcie przeglądu. Będziemy informować Cię o "
"wszelkich postępach."

#: templates/emails/application_status_change.html:67
msgid "Your application has reached the final stage of our selection process."
msgstr "Twoja aplikacja osiągnęła końcowy etap naszego procesu selekcji."

#: templates/emails/application_status_change.html:74
msgid ""
"While we were impressed with your qualifications, we have decided to move "
"forward with other candidates for this particular position."
msgstr ""
"Chociaż byliśmy pod wrażeniem Twoich kwalifikacji, zdecydowaliśmy się "
"kontynuować z innymi kandydatami na to konkretne stanowisko."

#: templates/emails/application_status_change.html:77
msgid ""
"We encourage you to apply for future opportunities that match your skills "
"and experience. We will keep your profile in our talent database for "
"consideration in upcoming roles."
msgstr ""
"Zachęcamy do aplikowania na przyszłe możliwości, które pasują do Twoich "
"umiejętności i doświadczenia. Zachowamy Twój profil w naszej bazie talentów "
"do rozważenia w nadchodzących rolach."

#: templates/emails/application_status_change.html:83
msgid ""
"We will continue to review your application and keep you informed of any "
"updates throughout the process."
msgstr ""
"Będziemy kontynuować przegląd Twojej aplikacji i informować Cię o wszelkich "
"aktualizacjach w trakcie procesu."

#: templates/emails/application_status_change.html:90
msgid ""
"Thank you for your interest in joining our team. We appreciate the time you "
"have invested in the application process."
msgstr ""
"Dziękujemy za zainteresowanie dołączeniem do naszego zespołu. Doceniamy "
"czas, który zainwestowałeś w proces aplikacji."

# Email-related translations
#: templates/emails/base_email.html:219 templates/emails/base_email.html:236
msgid "Recruitment Team"
msgstr "Zespół Rekrutacyjny"

#: templates/emails/base_email.html:226 templates/emails/team_invitation.html:9
msgid "Dear"
msgstr "Szanowny/a"

#: templates/emails/base_email.html:237
msgid "Human Resources Department"
msgstr "Dział Zasobów Ludzkich"

#: templates/emails/base_email.html:244
msgid "If you have any questions, please don't hesitate to contact us."
msgstr "Jeśli masz jakiekolwiek pytania, nie wahaj się z nami skontaktować."

#: templates/emails/base_email.html:248
msgid "Reference ID:"
msgstr "ID Referencyjne:"

#: templates/emails/base_email.html:252
msgid "This email was sent by"
msgstr "Ten e-mail został wysłany przez"

# General Message Email
#: templates/emails/general_message.html:6
msgid "Message from Recruitment Team"
msgstr "Wiadomość od Zespołu Rekrutacyjnego"

#: templates/emails/general_message.html:12
#, python-format
msgid ""
"\n"
"        This message is regarding your application for the position of "
"<strong>%(job_title)s</strong>.\n"
"        "
msgstr ""
"\n"
"        Ta wiadomość dotyczy Twojej aplikacji na stanowisko "
"<strong>%(job_title)s</strong>.\n"
"        "

#: templates/emails/general_message.html:36
msgid "Current Status"
msgstr "Aktualny Status"

#: templates/emails/general_message.html:50
msgid ""
"Thank you for your continued interest in our company. We appreciate your "
"patience throughout the application process."
msgstr ""
"Dziękujemy za ciągłe zainteresowanie naszą firmą. Doceniamy Twoją "
"cierpliwość podczas procesu aplikacji."

# Meeting Invitation Email
#: templates/emails/meeting_invitation.html:4
msgid "Meeting Invitation"
msgstr "Zaproszenie na Spotkanie"

#: templates/emails/meeting_invitation.html:6
msgid "Interview Invitation"
msgstr "Zaproszenie na Rozmowę Kwalifikacyjną"

#: templates/emails/meeting_invitation.html:11
msgid "Great news! We would like to invite you for an interview."
msgstr ""
"Świetne wiadomości! Chcielibyśmy zaprosić Cię na rozmowę kwalifikacyjną."

#: templates/emails/meeting_invitation.html:14
#, python-format
msgid ""
"\n"
"        We are pleased to invite you to an interview for the position of "
"<strong>%(job_title)s</strong>. \n"
"        We were impressed with your application and would like to learn more "
"about you.\n"
"        "
msgstr ""
"\n"
"        Mamy przyjemność zaprosić Cię na rozmowę kwalifikacyjną na stanowisko "
"<strong>%(job_title)s</strong>. \n"
"        Byliśmy pod wrażeniem Twojej aplikacji i "
"chcielibyśmy dowiedzieć się o Tobie więcej.\n"
"        "

#: templates/emails/meeting_invitation.html:23
msgid "Meeting Details"
msgstr "Szczegóły Spotkania"

#: templates/emails/meeting_invitation.html:27
msgid "Meeting Title"
msgstr "Tytuł Spotkania"

#: templates/emails/meeting_invitation.html:35
msgid "Interview Type"
msgstr "Typ Rozmowy"

#: templates/emails/meeting_invitation.html:38
msgid "Video Call"
msgstr ""

#: templates/emails/meeting_invitation.html:40
msgid "Phone Call"
msgstr ""

# msgid "Video Call"
# msgstr "Rozmowa Wideo"
# msgid "Phone Call"
# msgstr "Rozmowa Telefoniczna"
#: templates/emails/meeting_invitation.html:42
msgid "In-Person Meeting"
msgstr "Spotkanie Osobiste"

#: templates/emails/meeting_invitation.html:49
msgid "Date & Time"
msgstr "Data i Czas"

#: templates/emails/meeting_invitation.html:59
msgid "Duration"
msgstr "Czas Trwania"

#: templates/emails/meeting_invitation.html:68
msgid "Interviewer(s)"
msgstr "Prowadzący Rozmowę"

# msgid "Meeting Link"
# msgstr "Link do Spotkania"
#: templates/emails/meeting_invitation.html:81
msgid "Join Meeting"
msgstr "Dołącz do Spotkania"

#: templates/emails/meeting_invitation.html:92
msgid "Video Call Instructions"
msgstr "Instrukcje Rozmowy Wideo"

#: templates/emails/meeting_invitation.html:94
msgid "Please test your camera and microphone before the meeting"
msgstr "Proszę przetestować kamerę i mikrofon przed spotkaniem"

#: templates/emails/meeting_invitation.html:95
msgid "Ensure you have a stable internet connection"
msgstr "Upewnij się, że masz stabilne połączenie internetowe"

#: templates/emails/meeting_invitation.html:96
msgid "Find a quiet, well-lit space for the interview"
msgstr "Znajdź ciche, dobrze oświetlone miejsce na rozmowę"

#: templates/emails/meeting_invitation.html:97
msgid "Click the meeting link 5 minutes before the scheduled time"
msgstr "Kliknij link do spotkania 5 minut przed zaplanowanym czasem"

#: templates/emails/meeting_invitation.html:102
msgid "Phone Call Instructions"
msgstr "Instrukcje Rozmowy Telefonicznej"

#: templates/emails/meeting_invitation.html:104
msgid "Please ensure you're in a quiet location"
msgstr "Proszę upewnić się, że jesteś w cichym miejscu"

#: templates/emails/meeting_invitation.html:105
msgid "Have a good phone signal or use a landline if possible"
msgstr ""
"Miej dobry sygnał telefonu lub użyj telefonu stacjonarnego, jeśli to możliwe"

#: templates/emails/meeting_invitation.html:106
msgid "We will call you at the scheduled time"
msgstr "Zadzwonimy do Ciebie o zaplanowanej godzinie"

#: templates/emails/meeting_invitation.html:107
msgid "Have your resume and any questions ready"
msgstr "Przygotuj swoje CV i wszelkie pytania"

#: templates/emails/meeting_invitation.html:112
msgid "In-Person Meeting Instructions"
msgstr "Instrukcje Spotkania Osobistego"

#: templates/emails/meeting_invitation.html:114
msgid "Please arrive 10 minutes early"
msgstr "Proszę przybyć 10 minut wcześniej"

#: templates/emails/meeting_invitation.html:115
msgid "Bring a printed copy of your resume"
msgstr "Przynieś wydrukowaną kopię swojego CV"

#: templates/emails/meeting_invitation.html:116
msgid "Dress professionally"
msgstr "Ubierz się profesjonalnie"

#: templates/emails/meeting_invitation.html:117
msgid "Ask for directions at the reception if needed"
msgstr "Zapytaj o drogę w recepcji, jeśli potrzeba"

#: templates/emails/meeting_invitation.html:123
msgid "What to Expect"
msgstr "Czego Się Spodziewać"

#: templates/emails/meeting_invitation.html:125
msgid ""
"During the interview, we will discuss your background, experience, and how "
"you can contribute to our team. This is also a great opportunity for you to "
"ask questions about the role and our company culture."
msgstr ""
"Podczas rozmowy omówimy Twoje doświadczenie, kwalifikacje i to, jak możesz "
"przyczynić się do naszego zespołu. To także świetna okazja, aby zadać "
"pytania o rolę i kulturę naszej firmy."

#: templates/emails/meeting_invitation.html:130
msgid "Need to Reschedule?"
msgstr "Potrzebujesz Przełożyć?"

#: templates/emails/meeting_invitation.html:132
msgid ""
"If you need to reschedule this interview, please contact us as soon as "
"possible. When reaching out, please include the reference ID mentioned at "
"the bottom of this email."
msgstr ""
"Jeśli musisz przełożyć tę rozmowę, skontaktuj się z nami jak najszybciej. "
"Kontaktując się, proszę podać ID referencyjne wymienione na dole tego e-"
"maila."

#: templates/emails/meeting_invitation.html:138
msgid ""
"We look forward to meeting with you and learning more about your "
"qualifications!"
msgstr "Cieszymy się na spotkanie z Tobą i poznanie Twoich kwalifikacji!"

# Team Invitation Email
#: templates/emails/team_invitation.html:4
msgid "Team Invitation"
msgstr "Zaproszenie do Zespołu"

#: templates/emails/team_invitation.html:6
msgid "Join Our Team"
msgstr "Dołącz do Naszego Zespołu"

#: templates/emails/team_invitation.html:15
msgid "You've been invited to join our team!"
msgstr "Zostałeś zaproszony do dołączenia do naszego zespołu!"

#: templates/emails/team_invitation.html:18
#, python-format
msgid ""
"\n"
"        You have been invited to join the <strong>%(company_name)s</strong> "
"team at Canvider ATS as a <strong>%(role)s</strong>.\n"
"        "
msgstr ""
"\n"
"        Zostałeś zaproszony do dołączenia do zespołu <strong>%(company_name)s</strong> "
"w Canvider ATS jako <strong>%(role)s</strong>.\n"
"        "

#: templates/emails/team_invitation.html:26
msgid "Invitation Details"
msgstr "Szczegóły Zaproszenia"

#: templates/emails/team_invitation.html:30 templates/profile.html:66
#: templates/profile.html:111
msgid "Company"
msgstr "Firma"

#: templates/emails/team_invitation.html:34
#: templates/manage_permissions.html:113 templates/manage_permissions.html:252
#: templates/manage_permissions.html:381
msgid "Role"
msgstr "Rola"

# msgid "Company"
# msgstr "Firma"
# msgid "Role"
# msgstr "Rola"
#: templates/emails/team_invitation.html:38
msgid "Invitation Expires"
msgstr "Zaproszenie Wygasa"

#: templates/emails/team_invitation.html:46 templates/register.html:11
msgid "Accept Invitation"
msgstr "Zaakceptuj zaproszenie"

# msgid "Accept Invitation"
# msgstr "Zaakceptuj Zaproszenie"
#: templates/emails/team_invitation.html:51
msgid "Can't click the button?"
msgstr "Nie możesz kliknąć przycisku?"

#: templates/emails/team_invitation.html:53
msgid "Copy and paste this link into your browser:"
msgstr "Skopiuj i wklej ten link do swojej przeglądarki:"

#: templates/emails/team_invitation.html:61
msgid "Important Information"
msgstr "Ważne Informacje"

#: templates/emails/team_invitation.html:63
msgid "This invitation is valid until"
msgstr "To zaproszenie jest ważne do"

#: templates/emails/team_invitation.html:64
msgid "You will need to create a password when accepting the invitation"
msgstr "Będziesz musiał utworzyć hasło podczas akceptowania zaproszenia"

#: templates/emails/team_invitation.html:65
msgid "If you have any questions, please contact the administrator"
msgstr "Jeśli masz jakiekolwiek pytania, skontaktuj się z administratorem"

#: templates/emails/team_invitation.html:71
msgid "We look forward to having you on our team!"
msgstr "Cieszymy się na Ciebie w naszym zespole!"

#: templates/feed.html:20 templates/feed.html:46
msgid "Loading..."
msgstr "Ładowanie..."

#: templates/feed.html:32
msgid "Calendar"
msgstr "Kalendarz"

#: templates/feed.html:34
msgid "Day"
msgstr "Dzień"

#: templates/feed.html:35
msgid "Week"
msgstr "Tydzień"

#: templates/feed.html:36
msgid "Month"
msgstr "Miesiąc"

#: templates/feed.html:51 templates/jobs.html:92 templates/people.html:64
msgid "Today"
msgstr "Dziś"

#: templates/feed.html:56
msgid "Click on a day with colored dots to view events"
msgstr "Kliknij dzień z kolorowymi kropkami, aby zobaczyć wydarzenia"

#: templates/feed.html:61 templates/feed.html:74
msgid "Mon"
msgstr "Pon"

#: templates/feed.html:62 templates/feed.html:75
msgid "Tue"
msgstr "Wt"

#: templates/feed.html:63 templates/feed.html:76
msgid "Wed"
msgstr "Śr"

#: templates/feed.html:64 templates/feed.html:77
msgid "Thu"
msgstr "Czw"

#: templates/feed.html:65 templates/feed.html:78
msgid "Fri"
msgstr "Pt"

#: templates/feed.html:66 templates/feed.html:79
msgid "Sat"
msgstr "Sob"

#: templates/feed.html:67 templates/feed.html:80
msgid "Sun"
msgstr "Niedz"

#: templates/feed.html:96
msgid "Activity Feed"
msgstr "Aktywność"

#: templates/feed.html:100
msgid "Clear"
msgstr "Wyczyść"

#: templates/feed.html:147 templates/feed.html:3030
msgid "No Recent Activity"
msgstr "Brak ostatniej aktywności"

#: templates/feed.html:149 templates/feed.html:3032
msgid ""
"Activity will appear here when candidates apply, change status, or when you "
"post new jobs."
msgstr ""
"Aktywność pojawi się tutaj, gdy kandydaci aplikują, zmieniają status lub gdy "
"publikujesz nowe oferty."

#: templates/feed.html:166
msgid "Hot"
msgstr "Gorące"

#: templates/feed.html:166 templates/navbar.html:36
msgid "Jobs"
msgstr "Oferty pracy"

#: templates/feed.html:169
msgid "View All Jobs"
msgstr "Zobacz wszystkie oferty"

#: templates/feed.html:186 templates/feed.html:245 templates/jobs.html:129
#: templates/navbar.html:42
msgid "Applicants"
msgstr "Kandydaci"

#: templates/feed.html:203
msgid "No Hot Jobs Yet"
msgstr "Brak gorących ofert"

#: templates/feed.html:205
msgid ""
"Create your first job posting to start attracting candidates and see "
"trending positions here."
msgstr ""
"Utwórz pierwszą ofertę pracy, aby zacząć przyciągać kandydatów i zobaczyć "
"popularne stanowiska tutaj."

#: templates/feed.html:209 templates/navbar.html:57
msgid "Create Job"
msgstr "Utwórz ofertę"

#: templates/feed.html:212 templates/jobs.html:175
msgid "Browse Templates"
msgstr "Przeglądaj szablony"

#: templates/feed.html:228
msgid "Monthly Applicant Overview"
msgstr "Przegląd kandydatów miesięcznie"

#: templates/feed.html:267
msgid "Events for Date"
msgstr "Wydarzenia na dzień"

#: templates/feed.html:289
msgid "Add New Event"
msgstr "Dodaj nowe wydarzenie"

#: templates/feed.html:299
msgid "Create New Event"
msgstr "Utwórz nowe wydarzenie"

#: templates/feed.html:306
msgid "Enter event title"
msgstr "Wpisz tytuł wydarzenia"

#: templates/feed.html:393
msgid "Select the relevant position"
msgstr "Wybierz odpowiednie stanowisko"

#: templates/feed.html:400
msgid "No vacancies available"
msgstr "Brak dostępnych wakatów"

#: templates/feed.html:413
msgid "Pick a Vacancy to see candidates"
msgstr "Wybierz wakat, aby zobaczyć kandydatów"

#: templates/feed.html:436
msgid "Enter meeting link"
msgstr "Wpisz link do spotkania"

#: templates/feed.html:2975
msgid ""
"Are you sure you want to clear all activity notifications? This action "
"cannot be undone."
msgstr ""
"Czy na pewno chcesz wyczyścić wszystkie powiadomienia o aktywności? Tej "
"czynności nie można cofnąć."

#: templates/feed.html:3046
msgid "Activity feed cleared successfully"
msgstr "Powiadomienia o aktywności wyczyszczone"

#: templates/feed.html:3048
msgid "Failed to clear activity feed"
msgstr "Nie udało się wyczyścić powiadomień"

#: templates/feed.html:3053
msgid "An error occurred while clearing activity feed"
msgstr "Wystąpił błąd podczas czyszczenia powiadomień"

#: templates/job_details.html:7 templates/job_details.html:20
#: templates/job_details.html:51 templates/job_preview_publish.html:28
msgid "Job Description"
msgstr "Opis stanowiska"

#: templates/job_details.html:11 templates/job_preview_publish.html:17
msgid "Job Summary"
msgstr "Podsumowanie stanowiska"

#: templates/job_details.html:14 templates/job_preview_publish.html:21
msgid "Loading job details..."
msgstr "Ładowanie szczegółów stanowiska..."

#: templates/job_details.html:24
msgid "Create new description"
msgstr "Utwórz nowy opis"

#: templates/job_details.html:28
msgid "Use saved template"
msgstr "Użyj zapisanego szablonu"

#: templates/job_details.html:33
msgid "Choose a template:"
msgstr "Wybierz szablon:"

#: templates/job_details.html:35
msgid "Select a template"
msgstr "Wybierz szablon"

#: templates/job_details.html:42
msgid "AI Job Description Generator"
msgstr "Generator opisu stanowiska AI"

#: templates/job_details.html:43
msgid ""
"Let AI create a professional job description based on your job details above."
msgstr ""
"Pozwól AI stworzyć profesjonalny opis stanowiska na podstawie powyższych "
"szczegółów."

#: templates/job_details.html:46
msgid "Generate with AI"
msgstr "Generuj z AI"

#: templates/job_details.html:52
msgid ""
"Describe the position, responsibilities, qualifications, and any other "
"relevant details."
msgstr "Opisz stanowisko, obowiązki, kwalifikacje i inne istotne szczegóły."

#: templates/job_details.html:106 templates/job_preview_publish.html:158
msgid "Back"
msgstr "Wstecz"

#: templates/job_details.html:111
msgid "Update Template"
msgstr "Aktualizuj szablon"

#: templates/job_details.html:114 templates/job_details.html:124
msgid "Save as Template"
msgstr "Zapisz jako szablon"

#: templates/job_details.html:117
msgid "Save & Continue"
msgstr "Zapisz i kontynuuj"

#: templates/job_details.html:129
msgid "Template Title"
msgstr "Tytuł szablonu"

#: templates/job_details.html:130
msgid "Enter a name for this template"
msgstr "Wpisz nazwę dla tego szablonu"

#: templates/job_details.html:1060
msgid ""
"No job information found. Please go back and fill out the job details form."
msgstr ""
"Nie znaleziono informacji o stanowisku. Wróć i wypełnij formularz szczegółów."

#: templates/job_details.html:1093 templates/job_preview_publish.html:614
msgid "Salary Details"
msgstr "Szczegóły wynagrodzenia"

#: templates/job_details.html:1098 templates/job_preview_publish.html:621
msgid "Benefits & Highlights"
msgstr "Benefity i atuty"

#: templates/job_details.html:1108 templates/job_preview_publish.html:637
msgid "Skills"
msgstr "Umiejętności"

#: templates/job_preferences.html:11 templates/job_preferences.html:17
#: templates/settings.html:20
msgid "Preferences"
msgstr "Preferencje"

#: templates/job_preferences.html:12
msgid "Configure standard options to streamline your job creation process"
msgstr "Skonfiguruj standardowe opcje, aby usprawnić proces tworzenia ofert"

#: templates/job_preferences.html:26 templates/job_preferences.html:51
msgid "Work Schedules"
msgstr "Harmonogramy pracy"

#: templates/job_preferences.html:30 templates/job_preferences.html:84
msgid "Office Schedules"
msgstr "Harmonogramy biura"

#: templates/job_preferences.html:34
msgid "Locations"
msgstr "Lokalizacje"

#: templates/job_preferences.html:38
msgid "Departments"
msgstr "Działy"

#: templates/job_preferences.html:42
msgid "Language"
msgstr "Język"

#: templates/job_preferences.html:52
msgid "Define standard work schedule types for your organization"
msgstr "Zdefiniuj standardowe typy harmonogramów pracy dla swojej organizacji"

#: templates/job_preferences.html:56
msgid "Add Work Schedule"
msgstr "Dodaj harmonogram pracy"

#: templates/job_preferences.html:64
msgid "Search work schedules..."
msgstr "Wyszukaj harmonogramy pracy..."

#: templates/job_preferences.html:67
msgid "Select All"
msgstr "Wybierz wszystkie"

#: templates/job_preferences.html:85
msgid "Define where and how employees work"
msgstr "Zdefiniuj gdzie i jak pracują pracownicy"

#: templates/job_preferences.html:89
msgid "Add Office Schedule"
msgstr "Dodaj harmonogram biura"

#: templates/job_preferences.html:183
msgid "Language Settings"
msgstr "Ustawienia języka"

#: templates/job_preferences.html:184
msgid "Choose your preferred language for the application interface"
msgstr "Wybierz preferowany język interfejsu aplikacji"

#: templates/job_preferences.html:189
msgid "Interface Language"
msgstr "Język interfejsu"

#: templates/job_preferences.html:190
msgid "Select the language you want to use for the application interface"
msgstr "Wybierz język, którego chcesz używać w interfejsie aplikacji"

#: templates/job_preferences.html:210
msgid "Current"
msgstr "Aktualny"

#: templates/job_preferences.html:223 templates/published_job_details.html:652
#: templates/workloupe_platform.html:213
msgid "Note:"
msgstr "Uwaga:"

#: templates/job_preferences.html:223
msgid ""
"Changing the language will refresh the page to apply the new language "
"settings."
msgstr ""
"Zmiana języka odświeży stronę, aby zastosować nowe ustawienia językowe."

#: templates/job_preview_publish.html:7 templates/job_preview_publish.html:162
msgid "Publish Job"
msgstr "Opublikuj ofertę"

#: templates/job_preview_publish.html:11
msgid "Final Review"
msgstr "Ostateczny przegląd"

#: templates/job_preview_publish.html:12
msgid "Please review the job details before publishing."
msgstr "Przed publikacją sprawdź szczegóły oferty."

#: templates/job_preview_publish.html:32
msgid "Loading job description..."
msgstr "Ładowanie opisu stanowiska..."

#: templates/job_preview_publish.html:39
msgid "Publish To"
msgstr "Opublikuj na"

#: templates/job_preview_publish.html:41
msgid ""
"Select the job portals where you want to publish this job posting. <br> <br> "
"<i> if the portal you want to publish to is grayed out, it means that you "
"have not yet adjusted the related configuration settings. </i>"
msgstr ""
"Wybierz portale pracy, na których chcesz opublikować tę ofertę. <br> <br> "
"<i>Jeśli portal, na którym chcesz publikować, jest wyszarzony, oznacza to, "
"że nie dostosowałeś jeszcze powiązanych ustawień konfiguracyjnych.</i>"

#: templates/job_preview_publish.html:61
msgid ""
"Professional networking platform with over 750 million users worldwide. "
"Selecting this option will open a new tab for you to complete the job "
"posting."
msgstr ""
"Profesjonalna platforma sieciowa z ponad 750 milionami użytkowników na całym "
"świecie. Wybranie tej opcji spowoduje otwarcie nowej karty, aby uzupełnić "
"ofertę pracy."

#: templates/job_preview_publish.html:82
msgid ""
"Job and company review site focusing on workplace transparency. Selecting "
"this option will open a new tab for you to complete the job posting."
msgstr ""
"Serwis z recenzjami pracy i firm, skupiający się na przejrzystości miejsc "
"pracy. Wybranie tej opcji spowoduje otwarcie nowej karty, aby uzupełnić "
"ofertę pracy."

#: templates/job_preview_publish.html:103
#, python-format
msgid ""
"Specialized job platform for tech and creative professionals powered by "
"Workloupe. Workloupe is 100%% free to use."
msgstr ""
"Wyspecjalizowana platforma pracy dla profesjonalistów technicznych i "
"kreatywnych napędzana przez Workloupe. Workloupe jest w 100%% darmowe."

#: templates/job_preview_publish.html:125
msgid ""
"One of the biggest remote job focused job platforms in the world. Posting to "
"Himalayas is free. "
msgstr ""
"Jedna z największych platform pracy zdalnej na świecie. Publikowanie na "
"Himalayas jest darmowe."

#: templates/job_preview_publish.html:147
msgid ""
"PostJobFree has more than 7 million jobs, and it's free to post to. Their "
"job portal is focused on simplicity and ease of use."
msgstr ""
"PostJobFree ma ponad 7 milionów ofert pracy i publikowanie jest darmowe. Ich "
"portal pracy skupia się na prostocie i łatwości użytkowania."

#: templates/job_preview_publish.html:580
msgid ""
"No job information found. Please go back and fill out the job details form. "
msgstr ""
"Nie znaleziono informacji o stanowisku. Wróć i wypełnij formularz szczegółów."

#: templates/job_preview_publish.html:662
msgid "No job description found. Please go back and create a job description."
msgstr "Nie znaleziono opisu stanowiska. Wróć i utwórz opis stanowiska."

#: templates/jobs.html:8
msgid "Job Listings"
msgstr "Lista ofert pracy"

#: templates/jobs.html:18
msgid "Active Jobs"
msgstr "Aktywne oferty"

#: templates/jobs.html:28
msgid "Total Applicants"
msgstr "Łącznie kandydatów"

#: templates/jobs.html:38
msgid "Archived Jobs"
msgstr "Zarchiwizowane oferty"

#: templates/jobs.html:48
msgid "On-Hold Jobs"
msgstr "Oferty wstrzymane"

#: templates/jobs.html:61
msgid "All Departments"
msgstr "Wszystkie działy"

#: templates/jobs.html:71 templates/manage_permissions.html:212
#: templates/people.html:43
msgid "All Statuses"
msgstr "Wszystkie statusy"

#: templates/jobs.html:79 templates/people.html:51 templates/people.html:90
msgid "Location"
msgstr "Lokalizacja"

#: templates/jobs.html:81 templates/people.html:53
msgid "All Locations"
msgstr "Wszystkie lokalizacje"

#: templates/jobs.html:89
msgid "Posted Date"
msgstr "Data publikacji"

#: templates/jobs.html:91
msgid "All Time"
msgstr "Od zawsze"

#: templates/jobs.html:93 templates/people.html:65
msgid "This Week"
msgstr "W tym tygodniu"

#: templates/jobs.html:94 templates/people.html:66
msgid "This Month"
msgstr "W tym miesiącu"

#: templates/jobs.html:95
msgid "Last Month"
msgstr "W zeszłym miesiącu"

#: templates/jobs.html:96 templates/people.html:67
msgid "Last 7 Days"
msgstr "Ostatnie 7 dni"

#: templates/jobs.html:97 templates/people.html:68
msgid "Last 30 Days"
msgstr "Ostatnie 30 dni"

#: templates/jobs.html:98 templates/people.html:69
msgid "Last 3 Months"
msgstr "Ostatnie 3 miesiące"

#: templates/jobs.html:99 templates/people.html:70
msgid "Last 6 Months"
msgstr "Ostatnie 6 miesięcy"

#: templates/jobs.html:106 templates/people.html:76
msgid "Clear all filters"
msgstr "Wyczyść wszystkie filtry"

#: templates/jobs.html:135
msgid "Interviews"
msgstr "Rozmowy"

#: templates/jobs.html:142
msgid "Hired"
msgstr ""

#: templates/jobs.html:146
msgid "Days Open"
msgstr "Dni otwarte"

#: templates/jobs.html:152
msgid "Closed on:"
msgstr "Zamknięte:"

#: templates/jobs.html:152
msgid "Posted on:"
msgstr "Opublikowane:"

#: templates/jobs.html:153
msgid "View Details"
msgstr "Zobacz szczegóły"

#: templates/jobs.html:166
msgid "No Job Postings Yet"
msgstr "Brak ofert pracy"

#: templates/jobs.html:168
msgid ""
"You haven't published any job postings yet. Create your first job posting to "
"start attracting candidates."
msgstr ""
"Nie opublikowałeś jeszcze żadnych ofert pracy. Utwórz pierwszą ofertę, aby "
"zacząć przyciągać kandydatów."

#: templates/jobs.html:172
msgid "Create Your First Job"
msgstr "Utwórz pierwszą ofertę"

#: templates/jobs.html:186 templates/people.html:174
msgid "Showing"
msgstr "Wyświetlane"

#: templates/jobs.html:186 templates/people.html:174
msgid "of"
msgstr "z"

#: templates/manage_permissions.html:11
msgid "Team & Invitations"
msgstr "Zespół i zaproszenia"

#: templates/manage_permissions.html:12
msgid "Manage your recruitment team and invite new members"
msgstr "Zarządzaj zespołem rekrutacyjnym i zapraszaj nowych członków"

#: templates/manage_permissions.html:17 templates/manage_permissions.html:39
#: templates/manage_permissions.html:73 templates/settings.html:62
msgid "Invitations"
msgstr "Zaproszenia"

#: templates/manage_permissions.html:29 templates/manage_permissions.html:69
msgid "Team Members"
msgstr "Członkowie zespołu"

#: templates/manage_permissions.html:59
msgid "Administrators"
msgstr "Administratorzy"

#: templates/manage_permissions.html:84
msgid "Search team members..."
msgstr "Wyszukaj członków zespołu..."

#: templates/manage_permissions.html:88 templates/manage_permissions.html:218
msgid "Role:"
msgstr "Rola:"

#: templates/manage_permissions.html:90 templates/manage_permissions.html:220
msgid "All Roles"
msgstr "Wszystkie role"

#: templates/manage_permissions.html:105 templates/manage_permissions.html:244
#: templates/people.html:87 templates/profile.html:49
#: templates/published_job_details.html:276
msgid "Name"
msgstr "Imię i nazwisko"

#: templates/manage_permissions.html:109 templates/manage_permissions.html:248
#: templates/profile.html:57 templates/profile.html:101
#: templates/register.html:24
msgid "Email"
msgstr "E-mail"

#: templates/manage_permissions.html:120 templates/manage_permissions.html:267
#: templates/profile.html:80 templates/published_job_details.html:285
msgid "Actions"
msgstr "Akcje"

#: templates/manage_permissions.html:143
msgid "Deactivate"
msgstr "Dezaktywuj"

#: templates/manage_permissions.html:147
msgid "Activate"
msgstr "Aktywuj"

#: templates/manage_permissions.html:206
msgid "Search invitations..."
msgstr "Wyszukaj zaproszenia..."

#: templates/manage_permissions.html:210
msgid "Status:"
msgstr "Status:"

#: templates/manage_permissions.html:231
msgid "Invite New Member"
msgstr "Zaproś nowego członka"

#: templates/manage_permissions.html:256
msgid "Sent Date"
msgstr "Data wysłania"

#: templates/manage_permissions.html:260
msgid "Expiry Date"
msgstr "Data ważności"

#: templates/manage_permissions.html:305
msgid "No invitations found"
msgstr "Nie znaleziono zaproszeń"

#: templates/manage_permissions.html:353
msgid "Invite New Team Member"
msgstr "Zaproś nowego członka zespołu"

#: templates/manage_permissions.html:360
msgid "Recipient Information"
msgstr "Informacje o odbiorcy"

#: templates/manage_permissions.html:364 templates/profile.html:93
msgid "First Name"
msgstr "Imię"

#: templates/manage_permissions.html:365
msgid "Enter first name"
msgstr "Wpisz imię"

#: templates/manage_permissions.html:369 templates/profile.html:97
msgid "Last Name"
msgstr "Nazwisko"

#: templates/manage_permissions.html:370
msgid "Enter last name"
msgstr "Wpisz nazwisko"

#: templates/manage_permissions.html:375 templates/signin.html:17
msgid "Email Address"
msgstr "Adres e-mail"

#: templates/manage_permissions.html:376
msgid "Enter email address"
msgstr "Wpisz adres e-mail"

#: templates/manage_permissions.html:383 templates/manage_permissions.html:443
msgid "Select a role"
msgstr "Wybierz rolę"

#: templates/manage_permissions.html:384 templates/manage_permissions.html:444
msgid "Administrator"
msgstr "Administrator"

#: templates/manage_permissions.html:385 templates/manage_permissions.html:437
#: templates/manage_permissions.html:445
msgid "Recruiter"
msgstr "Rekruter"

#: templates/manage_permissions.html:386 templates/manage_permissions.html:446
msgid "Hiring Manager"
msgstr "Kierownik rekrutacji"

#: templates/manage_permissions.html:387 templates/manage_permissions.html:447
msgid "Interviewer"
msgstr "Osoba przeprowadzająca rozmowę"

#: templates/manage_permissions.html:388 templates/manage_permissions.html:448
msgid "Read Only"
msgstr "Tylko do odczytu"

#: templates/manage_permissions.html:395
msgid "Permissions"
msgstr "Uprawnienia"

#: templates/manage_permissions.html:396
msgid ""
"Permissions are determined by the selected role. You can customize them "
"after the user has accepted the invitation."
msgstr ""
"Uprawnienia są określane przez wybraną rolę. Możesz je dostosować po "
"zaakceptowaniu zaproszenia przez użytkownika."

#: templates/manage_permissions.html:400
msgid "Role Descriptions:"
msgstr "Opisy ról:"

#: templates/manage_permissions.html:402
msgid "Administrator: Full access to all system features and settings."
msgstr "Administrator: Pełny dostęp do wszystkich funkcji i ustawień systemu."

#: templates/manage_permissions.html:403
msgid "Recruiter: Manage job postings, candidates, and interviews."
msgstr "Rekruter: Zarządzaj ofertami pracy, kandydatami i rozmowami."

#: templates/manage_permissions.html:404
msgid "Hiring Manager: Review candidates and make hiring decisions."
msgstr ""
"Kierownik rekrutacji: Przeglądaj kandydatów i podejmuj decyzje o "
"zatrudnieniu."

#: templates/manage_permissions.html:405
msgid "Interviewer: Conduct interviews and provide feedback."
msgstr ""
"Osoba przeprowadzająca rozmowę: Przeprowadzaj rozmowy i przekazuj informacje "
"zwrotne."

#: templates/manage_permissions.html:406
msgid "Read Only: View-only access to recruitment data."
msgstr "Tylko do odczytu: Dostęp tylko do odczytu danych rekrutacyjnych."

#: templates/manage_permissions.html:414
msgid "Send Invitation"
msgstr "Wyślij zaproszenie"

#: templates/manage_permissions.html:425 templates/manage_permissions.html:464
msgid "Change Role"
msgstr "Zmień rolę"

#: templates/manage_permissions.html:431
msgid "Team Member"
msgstr "Członek zespołu"

#: templates/manage_permissions.html:436
msgid "Current Role"
msgstr "Aktualna rola"

#: templates/manage_permissions.html:441
msgid "New Role*"
msgstr "Nowa rola*"

#: templates/manage_permissions.html:453
msgid "Reason for Change (Optional)"
msgstr "Powód zmiany (opcjonalnie)"

#: templates/manage_permissions.html:454
msgid "Provide a reason for this role change"
msgstr "Podaj powód tej zmiany roli"

#: templates/manage_permissions.html:459
msgid ""
"Changing roles will update the user's permissions. They will be notified of "
"this change."
msgstr ""
"Zmiana ról zaktualizuje uprawnienia użytkownika. Użytkownik zostanie "
"powiadomiony o tej zmianie."

#: templates/navbar.html:31
msgid "Feed"
msgstr "Aktualności"

#: templates/navbar.html:94
msgid "Employee"
msgstr "Pracownik"

#: templates/navbar.html:100
msgid "Profile"
msgstr "Profil"

#: templates/navbar.html:109
msgid "Logout"
msgstr "Wyloguj"

#: templates/navbar.html:118
msgid "Guest User"
msgstr "Gość"

#: templates/navbar.html:121
msgid "Not logged in"
msgstr "Nie zalogowany"

#: templates/navbar.html:126 templates/signin.html:97
msgid "Sign In"
msgstr "Zaloguj się"

#: templates/people.html:10
msgid "Applicant Tracking"
msgstr "Śledzenie kandydatów"

#: templates/people.html:14 templates/people.html:1034
msgid "Refresh Applicants"
msgstr "Odśwież kandydatów"

#: templates/people.html:18
msgid "Search applicants..."
msgstr "Wyszukaj kandydatów..."

#: templates/people.html:33
msgid "All Positions"
msgstr "Wszystkie stanowiska"

#: templates/people.html:63
msgid "All Dates"
msgstr "Wszystkie daty"

#: templates/people.html:91
msgid "Experience (Years)"
msgstr "Doświadczenie (lata)"

#: templates/people.html:92 templates/published_job_details.html:282
msgid "Score"
msgstr "Wynik"

#: templates/people.html:93
msgid "Applied On"
msgstr "Data aplikacji"

#: templates/people.html:94
msgid "Action"
msgstr "Akcja"

#: templates/people.html:139 templates/published_job_details.html:343
msgid "View"
msgstr "Zobacz"

#: templates/people.html:154 templates/published_job_details.html:246
msgid "No Applicants Yet"
msgstr "Brak kandydatów"

#: templates/people.html:156
msgid ""
"Nobody has applied to your job postings yet. Once candidates start applying, "
"you'll see them here."
msgstr ""
"Nikt jeszcze nie aplikował na Twoje oferty pracy. Gdy kandydaci zaczną "
"aplikować, zobaczysz ich tutaj."

#: templates/people.html:160
msgid "View Job Postings"
msgstr "Zobacz oferty pracy"

#: templates/people.html:163
msgid "Create New Job"
msgstr "Utwórz nową ofertę"

#: templates/people.html:174
msgid "applicants"
msgstr "kandydatów"

#: templates/people.html:227
msgid "Show"
msgstr "Pokaż"

#: templates/people.html:234
msgid "per page"
msgstr "na stronę"

#: templates/people.html:969
msgid "Processing..."
msgstr "Przetwarzanie..."

#: templates/people.html:1015
msgid "Success!"
msgstr "Sukces!"

#: templates/people.html:1022 templates/workloupe_platform.html:1119
msgid "Error:"
msgstr "Błąd:"

#: templates/people.html:1027
msgid "An error occurred while processing applications. Please try again."
msgstr ""
"Wystąpił błąd podczas przetwarzania aplikacji. Proszę spróbować ponownie."

#: templates/profile.html:10
msgid "Your Profile"
msgstr "Twój profil"

#: templates/profile.html:29
msgid "Change Photo"
msgstr "Zmień zdjęcie"

#: templates/profile.html:32
msgid "Profile Activity"
msgstr "Aktywność profilu"

#: templates/profile.html:33
msgid "Last Login:"
msgstr "Ostatnie logowanie:"

#: templates/profile.html:36
msgid "Account Created:"
msgstr "Konto utworzono:"

#: templates/profile.html:46 templates/profile.html:91
msgid "Personal Information"
msgstr "Dane osobowe"

#: templates/profile.html:63 templates/profile.html:109
#: templates/workloupe_platform.html:28
msgid "Company Information"
msgstr "Informacje o firmie"

#: templates/profile.html:83 templates/profile.html:163
#: templates/profile.html:185
msgid "Change Password"
msgstr "Zmień hasło"

#: templates/profile.html:105
msgid "Phone"
msgstr "Telefon"

#: templates/profile.html:124
msgid "Save Changes"
msgstr "Zapisz zmiany"

#: templates/profile.html:139
msgid "Upload Profile Photo"
msgstr "Prześlij zdjęcie profilowe"

#: templates/profile.html:146
msgid "Choose a photo (PNG or JPEG only)"
msgstr "Wybierz zdjęcie (tylko PNG lub JPEG)"

#: templates/profile.html:153
msgid "Upload"
msgstr "Prześlij"

#: templates/profile.html:170
msgid "Current Password"
msgstr "Aktualne hasło"

#: templates/profile.html:174
msgid "New Password"
msgstr "Nowe hasło"

#: templates/profile.html:178
msgid "Confirm New Password"
msgstr "Potwierdź nowe hasło"

#: templates/published_job_details.html:58
msgid "Notification"
msgstr "Powiadomienie"

#: templates/published_job_details.html:82
msgid "Total Applicants:"
msgstr "Łącznie kandydatów:"

#: templates/published_job_details.html:87
msgid "Published At:"
msgstr "Opublikowano:"

#: templates/published_job_details.html:111
msgid "Bulk Communication"
msgstr "Komunikacja zbiorowa"

#: templates/published_job_details.html:124
msgid "Expert Support Options"
msgstr "Opcje wsparcia eksperckiego"

#: templates/published_job_details.html:136
msgid "Post on LinkedIn"
msgstr "Opublikuj na LinkedIn"

#: templates/published_job_details.html:148
#: templates/published_job_details.html:641
msgid "Change Vacancy Status"
msgstr "Zmień status wakatu"

#: templates/published_job_details.html:162
msgid "Applicants Over Time"
msgstr "Kandydaci w czasie"

#: templates/published_job_details.html:170
msgid "No Application Data Yet"
msgstr "Brak danych aplikacyjnych"

#: templates/published_job_details.html:171
msgid ""
"Once candidates start applying, you'll see application trends over time here."
msgstr "Gdy kandydaci zaczną aplikować, zobaczysz trendy aplikacyjne w czasie."

#: templates/published_job_details.html:184
msgid "Number of Applicants by Job Portal"
msgstr "Liczba kandydatów według portalu pracy"

#: templates/published_job_details.html:191
msgid "No Portal Data Yet"
msgstr "Brak danych portalu"

#: templates/published_job_details.html:192
msgid ""
"When applications come in, you'll see which job portals are most effective "
"here."
msgstr ""
"Gdy aplikacje napłyną, zobaczysz, które portale pracy są najskuteczniejsze."

#: templates/published_job_details.html:202
msgid "Distribution of Applicants by Status"
msgstr "Rozkład kandydatów według statusu"

#: templates/published_job_details.html:209
msgid "No Status Data Yet"
msgstr "Brak danych statusu"

#: templates/published_job_details.html:210
msgid ""
"Application status distribution will appear here as you review candidates."
msgstr ""
"Rozkład statusów aplikacji pojawi się tutaj podczas przeglądania kandydatów."

#: templates/published_job_details.html:225
msgid "Top Applicants"
msgstr "Najlepsi kandydaci"

#: templates/published_job_details.html:233
msgid "View All Applicants"
msgstr "Zobacz wszystkich kandydatów"

#: templates/published_job_details.html:249
msgid ""
"Don't worry! Once candidates start applying to this position, they will "
"appear here. You can track their progress, review their profiles, and manage "
"the hiring process."
msgstr ""
"Bez obaw! Gdy kandydaci zaczną aplikować na to stanowisko, pojawią się "
"tutaj. Możesz śledzić ich postępy, przeglądać profile i zarządzać procesem "
"rekrutacji."

#: templates/published_job_details.html:256
msgid "What happens next?"
msgstr "Co dalej?"

#: templates/published_job_details.html:259
msgid "Candidates will apply through your job posting"
msgstr "Kandydaci będą aplikować przez Twoją ofertę pracy"

#: templates/published_job_details.html:260
msgid "You'll see their profiles and CVs here"
msgstr "Zobaczysz ich profile i CV tutaj"

#: templates/published_job_details.html:261
msgid "You can review, rate, and manage applications"
msgstr "Możesz przeglądać, oceniać i zarządzać aplikacjami"

#: templates/published_job_details.html:262
msgid "Use the communication tools to contact candidates"
msgstr "Użyj narzędzi komunikacji, aby kontaktować się z kandydatami"

#: templates/published_job_details.html:332
msgid "Not Rated"
msgstr "Nieocenione"

#: templates/published_job_details.html:360
msgid "Request Support From Experts"
msgstr "Poproś o wsparcie ekspertów"

#: templates/published_job_details.html:365
msgid ""
"We can provide vetted candidates from our talent pool, or help you during "
"the technical interviews to pick best fit for your expectations."
msgstr ""
"Możemy dostarczyć zweryfikowanych kandydatów z naszej puli talentów lub "
"pomóc podczas rozmów technicznych w wyborze najlepiej pasującego do Twoich "
"oczekiwań."

#: templates/published_job_details.html:370
msgid "Enter Details"
msgstr "Wprowadź szczegóły"

#: templates/published_job_details.html:381
msgid "Request Candidates"
msgstr "Poproś o kandydatów"

#: templates/published_job_details.html:382
msgid "Request Interview Help"
msgstr "Poproś o pomoc w rozmowie"

#: templates/published_job_details.html:396
msgid "Send Bulk Mail to Applicants"
msgstr "Wyślij zbiorczy e-mail do kandydatów"

#: templates/published_job_details.html:401
msgid ""
"Use this form to send bulk emails to applicants based on their application "
"statuses."
msgstr ""
"Użyj tego formularza, aby wysyłać zbiorcze e-maile do kandydatów na "
"podstawie statusów ich aplikacji."

#: templates/published_job_details.html:406
msgid "Select Application Status"
msgstr "Wybierz status aplikacji"

#: templates/published_job_details.html:413
msgid "Select a status"
msgstr "Wybierz status"

#: templates/published_job_details.html:420
msgid "Email Subject"
msgstr "Temat e-maila"

#: templates/published_job_details.html:439
msgid "Enter internal notes for your team (optional)"
msgstr "Wpisz wewnętrzne notatki dla swojego zespołu (opcjonalnie)"

#: templates/published_job_details.html:443
msgid "(sent to candidates)"
msgstr "(wysłane do kandydatów)"

#: templates/published_job_details.html:450
msgid "Enter your email message"
msgstr "Wpisz treść e-maila"

#: templates/published_job_details.html:462
msgid "Send notification emails to candidates"
msgstr "Wyślij e-maile powiadamiające do kandydatów"

#: templates/published_job_details.html:466
msgid "Send Emails"
msgstr "Wyślij e-maile"

#: templates/published_job_details.html:482
msgid "Post this job on LinkedIn"
msgstr "Opublikuj tę pracę na LinkedIn"

#: templates/published_job_details.html:489
msgid ""
"Follow these simple steps to post your job on LinkedIn and reach more "
"candidates."
msgstr ""
"Wykonaj te proste kroki, aby opublikować swoją ofertę na LinkedIn i dotrzeć "
"do większej liczby kandydatów."

#: templates/published_job_details.html:496
msgid "Navigate to LinkedIn Job Posting"
msgstr "Przejdź do publikowania ofert na LinkedIn"

#: templates/published_job_details.html:499
msgid "Go to"
msgstr "Przejdź do"

#: templates/published_job_details.html:499
msgid "LinkedIn Job Posting Page"
msgstr "Strona publikowania ofert LinkedIn"

#: templates/published_job_details.html:507
msgid "Copy Job Title"
msgstr "Skopiuj tytuł stanowiska"

#: templates/published_job_details.html:509
msgid "Copy and paste the job title below:"
msgstr "Skopiuj i wklej tytuł stanowiska poniżej:"

#: templates/published_job_details.html:526
msgid "Configure Job Settings"
msgstr "Skonfiguruj ustawienia oferty"

#: templates/published_job_details.html:529
msgid "Select 'Use my own description' option and configure job details:"
msgstr "Wybierz opcję 'Użyj własnego opisu' i skonfiguruj szczegóły oferty:"

#: templates/published_job_details.html:531
msgid "Reference - Your job settings:"
msgstr "Odniesienie - Twoje ustawienia oferty:"

#: templates/published_job_details.html:533
msgid "Location:"
msgstr "Lokalizacja:"

#: templates/published_job_details.html:534
msgid "Work Schedule:"
msgstr "Harmonogram pracy:"

#: templates/published_job_details.html:535
msgid "Office Schedule:"
msgstr "Harmonogram biura:"

#: templates/published_job_details.html:545
msgid "Copy Job Description"
msgstr "Skopiuj opis stanowiska"

#: templates/published_job_details.html:548
msgid "Copy and paste the job description below:"
msgstr "Skopiuj i wklej opis stanowiska poniżej:"

#: templates/published_job_details.html:551
msgid ""
"Note: Delete any placeholder text in LinkedIn's editor before pasting. You "
"may have to re-apply some of the styling."
msgstr ""
"Uwaga: Usuń wszystkie teksty zastępcze w edytorze LinkedIn przed wklejeniem. "
"Może być konieczne ponowne zastosowanie części formatowania."

#: templates/published_job_details.html:570
msgid "Configure Application Management"
msgstr "Skonfiguruj zarządzanie aplikacjami"

#: templates/published_job_details.html:574
msgid "Click 'Continue' button"
msgstr "Kliknij przycisk 'Kontynuuj'"

#: templates/published_job_details.html:575
msgid "Find 'Manage applicants' option and click the pencil icon to edit"
msgstr ""
"Znajdź opcję 'Zarządzaj kandydatami' i kliknij ikonę ołówka, aby edytować"

#: templates/published_job_details.html:576
msgid ""
"Change 'Manage Applications' from 'On LinkedIn' to 'On an External Website'"
msgstr ""
"Zmień 'Zarządzaj aplikacjami' z 'Na LinkedIn' na 'Na zewnętrznej stronie'"

#: templates/published_job_details.html:577
msgid "Copy and paste the application URL below:"
msgstr "Skopiuj i wklej adres URL aplikacji poniżej:"

#: templates/published_job_details.html:596
msgid "Review Qualifications"
msgstr "Przejrzyj kwalifikacje"

#: templates/published_job_details.html:599
msgid "Review and customize the ideal qualifications section:"
msgstr "Przejrzyj i dostosuj sekcję wymaganych kwalifikacji:"

#: templates/published_job_details.html:601
msgid "Reference - Skills from your job:"
msgstr "Odniesienie - Umiejętności z Twojej oferty:"

#: templates/published_job_details.html:615
msgid "Finalize and Publish"
msgstr "Zakończ i opublikuj"

#: templates/published_job_details.html:619
msgid "Confirm your identity using your work email if required"
msgstr ""
"Potwierdź swoją tożsamość przy użyciu służbowego e-maila, jeśli wymagane"

#: templates/published_job_details.html:620
msgid "Choose between free or promoted posting (Recommended: Free)"
msgstr ""
"Wybierz między bezpłatnym a promowanym ogłoszeniem (Rekomendowane: Bezpłatne)"

#: templates/published_job_details.html:621
msgid "Click 'Post Job' button"
msgstr "Kliknij przycisk 'Opublikuj ofertę'"

#: templates/published_job_details.html:622
msgid "Your job is now live on LinkedIn!"
msgstr "Twoja oferta jest teraz aktywna na LinkedIn!"

#: templates/published_job_details.html:629
msgid ""
"Need help? Contact our support team if you encounter any issues during the "
"posting process."
msgstr ""
"Potrzebujesz pomocy? Skontaktuj się z naszym zespołem wsparcia, jeśli "
"napotkasz jakiekolwiek problemy podczas publikowania."

#: templates/published_job_details.html:646
msgid "Current Status:"
msgstr "Aktualny status:"

#: templates/published_job_details.html:652
msgid "Changing the status will affect the visibility of the vacancy."
msgstr "Zmiana statusu wpłynie na widoczność wakatu."

#: templates/published_job_details.html:655
msgid ""
"The vacancy will no longer exist on boards and be closed but be accesible "
"internally."
msgstr ""
"Wakat nie będzie już istnieć na tablicach i zostanie zamknięty, ale będzie "
"dostępny wewnętrznie."

#: templates/published_job_details.html:656
msgid "The vacancy will stop accepting new applications until changed."
msgstr ""
"Wakat przestanie przyjmować nowe aplikacje, dopóki nie zostanie zmieniony."

#: templates/published_job_details.html:657
msgid "The vacancy will be re-opened for new applications."
msgstr "Wakat zostanie ponownie otwarty na nowe aplikacje."

#: templates/published_job_details.html:658
msgid "The vacancy will be permanently deleted. This action cannot be undone."
msgstr "Wakat zostanie trwale usunięty. Tej czynności nie można cofnąć."

#: templates/published_job_details.html:664
msgid "Select New Status"
msgstr "Wybierz nowy status"

#: templates/published_job_details.html:687
msgid "Confirm Status"
msgstr "Potwierdź status"

#: templates/register.html:15
msgid "This invitation has expired or already been used."
msgstr "To zaproszenie wygasło lub zostało już użyte."

#: templates/register.html:18
msgid "Hello"
msgstr "Witaj"

#: templates/register.html:18
msgid "you've been invited to join"
msgstr "zostałeś zaproszony do dołączenia do"

#: templates/register.html:18
msgid "as a"
msgstr "jako"

#: templates/register.html:29
msgid "Create Password"
msgstr "Utwórz hasło"

#: templates/register.html:34
msgid "Confirm Password"
msgstr "Potwierdź hasło"

#: templates/register.html:39
msgid "Complete Registration"
msgstr "Zakończ rejestrację"

#: templates/register.html:58
msgid "Passwords do not match"
msgstr "Hasła nie pasują do siebie"

#: templates/registration_complete.html:14
msgid "Registration Complete!"
msgstr "Rejestracja zakończona!"

#: templates/registration_complete.html:15
msgid ""
"Your account has been created successfully. You can now log in to access the "
"system."
msgstr ""
"Twoje konto zostało pomyślnie utworzone. Możesz się teraz zalogować, aby "
"uzyskać dostęp do systemu."

#: templates/registration_complete.html:17
msgid "Go to Login"
msgstr "Przejdź do logowania"

#: templates/settings.html:10
msgid "Configure your recruitment workflow and manage your ATS settings"
msgstr "Skonfiguruj swój proces rekrutacji i zarządzaj ustawieniami ATS"

#: templates/settings.html:21
msgid ""
"Configure default options for job creation including work schedules, office "
"locations, and role titles."
msgstr ""
"Skonfiguruj domyślne opcje tworzenia ofert, w tym harmonogramy pracy, "
"lokalizacje biur i tytuły stanowisk."

#: templates/settings.html:23
msgid "Define company work schedules"
msgstr "Zdefiniuj harmonogramy pracy w firmie"

#: templates/settings.html:24
msgid "Set up office locations"
msgstr "Skonfiguruj lokalizacje biur"

#: templates/settings.html:25
msgid "Standardize role titles"
msgstr "Standaryzuj tytuły stanowisk"

#: templates/settings.html:26
msgid "Configure office schedule options"
msgstr "Skonfiguruj opcje harmonogramu biura"

#: templates/settings.html:29
msgid "Manage Preferences"
msgstr "Zarządzaj preferencjami"

#: templates/settings.html:42
msgid ""
"Create, edit, and manage job description templates to streamline your job "
"posting process."
msgstr ""
"Twórz, edytuj i zarządzaj szablonami opisów stanowisk, aby usprawnić proces "
"publikowania ofert."

#: templates/settings.html:44
msgid "Build reusable job templates"
msgstr "Twórz wielokrotnego użytku szablony ofert"

#: templates/settings.html:45
msgid "Save time on repetitive descriptions"
msgstr "Oszczędzaj czas na powtarzalnych opisach"

#: templates/settings.html:46
msgid "Maintain consistent job postings"
msgstr "Utrzymuj spójność ofert pracy"

#: templates/settings.html:47
msgid "Organize templates by department"
msgstr "Organizuj szablony według działów"

#: templates/settings.html:50
msgid "Manage Templates"
msgstr "Zarządzaj szablonami"

#: templates/settings.html:63
msgid ""
"Invite team members to collaborate on your recruitment process and manage "
"user access."
msgstr ""
"Zaproś członków zespołu do współpracy w procesie rekrutacji i zarządzaj "
"dostępem użytkowników."

#: templates/settings.html:65
msgid "Add colleagues to your ATS"
msgstr "Dodaj kolegów do swojego ATS"

#: templates/settings.html:66
msgid "Set user permissions"
msgstr "Ustaw uprawnienia użytkowników"

#: templates/settings.html:67
msgid "Track invitation status"
msgstr "Śledź status zaproszeń"

#: templates/settings.html:68
msgid "Manage team collaboration"
msgstr "Zarządzaj współpracą zespołu"

#: templates/settings.html:71
msgid "Manage Invitations"
msgstr "Zarządzaj zaproszeniami"

#: templates/settings.html:84
msgid "Job Portals"
msgstr "Portale pracy"

#: templates/settings.html:85
msgid ""
"Configure connections to external job boards and manage API credentials for "
"job publishing."
msgstr ""
"Skonfiguruj połączenia z zewnętrznymi tablicami ofert pracy i zarządzaj "
"poświadczeniami API do publikacji ofert."

#: templates/settings.html:87
msgid "Connect to major job boards"
msgstr "Połącz się z głównymi tablicami ofert"

#: templates/settings.html:88
msgid "Manage API tokens securely"
msgstr "Bezpiecznie zarządzaj tokenami API"

#: templates/settings.html:89
msgid "Customize portal preferences"
msgstr "Dostosuj preferencje portalu"

#: templates/settings.html:90
msgid "Track portal integration status"
msgstr "Śledź status integracji portalu"

#: templates/settings.html:93
msgid "(Coming Soon!)"
msgstr "(Wkrótce!)"

#: templates/settings.html:106
msgid "Careers Page & Workloupe Configurations"
msgstr "Strona karier i konfiguracje Workloupe"

#: templates/settings.html:107
msgid ""
"Adjust the look and feel of your company careers page and workloupe profile "
"to match your branding."
msgstr ""
"Dostosuj wygląd i działanie strony karier Twojej firmy i profilu Workloupe "
"do Twojej marki."

#: templates/settings.html:109
msgid "Upload company photos"
msgstr "Prześlij zdjęcia firmy"

#: templates/settings.html:110
msgid "Pick your colours"
msgstr "Wybierz swoje kolory"

#: templates/settings.html:111
msgid "Choose the components"
msgstr "Wybierz komponenty"

#: templates/settings.html:112
msgid "Upload your company logo and banner"
msgstr "Prześlij logo i baner firmy"

#: templates/settings.html:115
msgid "Manage Carees Page & Workloupe Configurations"
msgstr "Zarządzaj stroną karier i konfiguracjami Workloupe"

#: templates/settings.html:126
msgid "Need Help?"
msgstr "Potrzebujesz pomocy?"

#: templates/settings.html:127
msgid ""
"Our support team is ready to assist you with any questions about configuring "
"your ATS."
msgstr ""
"Nasz zespół wsparcia jest gotowy pomóc Ci w każdej kwestii dotyczącej "
"konfiguracji Twojego ATS."

#: templates/settings.html:128
msgid "Contact Support"
msgstr "Skontaktuj się z pomocą techniczną"

#: templates/signin.html:9
msgid "Welcome Back"
msgstr "Witaj ponownie"

#: templates/signin.html:10
msgid "Sign in to your account"
msgstr "Zaloguj się na swoje konto"

#: templates/signin.html:24
msgid "Enter your email"
msgstr "Wpisz swój e-mail"

#: templates/signin.html:38
msgid "Password"
msgstr "Hasło"

#: templates/signin.html:45
msgid "Enter your password"
msgstr "Wpisz swoje hasło"

#: templates/signin.html:72
msgid "Security Check"
msgstr "Sprawdzenie bezpieczeństwa"

#: templates/signin.html:72
msgid "What is"
msgstr "Ile wynosi"

#: templates/signin.html:79
msgid "Enter the answer"
msgstr "Wpisz odpowiedź"

#: templates/signin.html:89
msgid "Remember me"
msgstr "Zapamiętaj mnie"

#: templates/signin.html:92
msgid "Forgot password?"
msgstr "Zapomniałeś hasła?"

#: templates/signin.html:106
msgid "Don't have an account?"
msgstr "Nie masz konta?"

#: templates/signin.html:108
msgid "Contact us"
msgstr "Skontaktuj się z nami"

#: templates/wordpress_integration.html:14
msgid "WordPress Integration"
msgstr "Integracja z WordPress"

#: templates/wordpress_integration.html:15
msgid "Configure your WordPress careers page"
msgstr "Skonfiguruj swoją stronę karier WordPress"

#: templates/wordpress_integration.html:23
msgid "WordPress Setup"
msgstr "Konfiguracja WordPress"

#: templates/wordpress_integration.html:28
msgid "Choose your preferred WordPress integration method"
msgstr "Wybierz preferowaną metodę integracji WordPress"

#: templates/wordpress_integration.html:32
msgid "Integration Method"
msgstr "Metoda integracji"

#: templates/wordpress_integration.html:34
msgid "Shortcode (Recommended)"
msgstr "Shortcode (Rekomendowane)"

#: templates/wordpress_integration.html:35
#: templates/wordpress_integration.html:187
msgid "WordPress Widget"
msgstr "Widget WordPress"

#: templates/wordpress_integration.html:36
#: templates/wordpress_integration.html:201
msgid "Custom Plugin"
msgstr "Niestandardowa wtyczka"

#: templates/wordpress_integration.html:63
msgid "Design Settings"
msgstr "Ustawienia projektu"

#: templates/wordpress_integration.html:72
msgid "WordPress Theme Style"
msgstr "Styl motywu WordPress"

#: templates/wordpress_integration.html:74
msgid "Inherit from Theme"
msgstr "Dziedzicz z motywu"

#: templates/wordpress_integration.html:84
msgid "Responsive Design"
msgstr "Projekt responsywny"

#: templates/wordpress_integration.html:97
msgid "Jobs Per Page"
msgstr "Ofert na stronę"

#: templates/wordpress_integration.html:109
msgid "Show Job Filters"
msgstr "Pokaż filtry ofert"

#: templates/wordpress_integration.html:116
msgid "Show Search Box"
msgstr "Pokaż pole wyszukiwania"

#: templates/wordpress_integration.html:123
msgid "Show Pagination"
msgstr "Pokaż paginację"

#: templates/wordpress_integration.html:132
msgid "Generate WordPress Code"
msgstr "Generuj kod WordPress"

#: templates/wordpress_integration.html:141
msgid "Preview & Instructions"
msgstr "Podgląd i instrukcje"

#: templates/wordpress_integration.html:144
msgid "Preview"
msgstr "Podgląd"

#: templates/wordpress_integration.html:147
msgid "Instructions"
msgstr "Instrukcje"

#: templates/wordpress_integration.html:167
msgid "Shortcode Integration"
msgstr "Integracja shortcode"

#: templates/wordpress_integration.html:169
msgid "Recommended Method"
msgstr "Rekomendowana metoda"

#: templates/wordpress_integration.html:169
msgid "Easy to use and works with any WordPress theme"
msgstr "Łatwy w użyciu i działa z dowolnym motywem WordPress"

#: templates/wordpress_integration.html:172
msgid "Copy the shortcode below"
msgstr "Skopiuj poniższy shortcode"

#: templates/wordpress_integration.html:173
msgid "Go to your WordPress admin panel"
msgstr "Przejdź do panelu administratora WordPress"

#: templates/wordpress_integration.html:174
msgid "Edit the page where you want to display jobs"
msgstr "Edytuj stronę, na której chcesz wyświetlać oferty"

#: templates/wordpress_integration.html:175
msgid "Paste the shortcode in the content area"
msgstr "Wklej shortcode w obszarze treści"

#: templates/wordpress_integration.html:176
msgid "Save and publish the page"
msgstr "Zapisz i opublikuj stronę"

#: templates/wordpress_integration.html:189
msgid "Perfect for sidebars and widget areas"
msgstr "Idealne do pasków bocznych i obszarów widgetów"

#: templates/wordpress_integration.html:192
msgid "Go to Appearance > Widgets in your WordPress admin"
msgstr "Przejdź do Wygląd > Widgety w panelu administratora WordPress"

#: templates/wordpress_integration.html:193
msgid "Find the 'Workloupe Careers' widget"
msgstr "Znajdź widget 'Workloupe Careers'"

#: templates/wordpress_integration.html:194
msgid "Drag it to your desired widget area"
msgstr "Przeciągnij go do pożądanego obszaru widgetów"

#: templates/wordpress_integration.html:195
msgid "Configure the widget settings"
msgstr "Skonfiguruj ustawienia widgetu"

#: templates/wordpress_integration.html:196
msgid "Save the widget"
msgstr "Zapisz widget"

#: templates/wordpress_integration.html:203
msgid "Advanced option - requires technical knowledge"
msgstr "Opcja zaawansowana - wymaga wiedzy technicznej"

#: templates/wordpress_integration.html:206
msgid "Download the custom plugin file"
msgstr "Pobierz plik niestandardowej wtyczki"

#: templates/wordpress_integration.html:207
msgid "Upload it to your WordPress plugins directory"
msgstr "Prześlij go do katalogu wtyczek WordPress"

#: templates/wordpress_integration.html:208
msgid "Activate the plugin in WordPress admin"
msgstr "Aktywuj wtyczkę w panelu administratora WordPress"

#: templates/wordpress_integration.html:209
msgid "Configure the plugin settings"
msgstr "Skonfiguruj ustawienia wtyczki"

#: templates/wordpress_integration.html:210
msgid "Use shortcodes or widgets as needed"
msgstr "Używaj shortcode'ów lub widgetów w razie potrzeby"

#: templates/wordpress_integration.html:214
msgid "Download Plugin"
msgstr "Pobierz wtyczkę"

#: templates/wordpress_integration.html:231
msgid "WordPress Integration Code"
msgstr "Kod integracji WordPress"

#: templates/wordpress_integration.html:238
msgid "Copy the code below and follow the integration instructions."
msgstr "Skopiuj poniższy kod i postępuj zgodnie z instrukcjami integracji."

#: templates/wordpress_integration.html:243
msgid "WordPress Code"
msgstr "Kod WordPress"

#: templates/wordpress_integration.html:256
msgid "Download Files"
msgstr "Pobierz pliki"

#: templates/workloupe_platform.html:13
msgid "Workloupe Platform Setup"
msgstr "Konfiguracja platformy Workloupe"

#: templates/workloupe_platform.html:14
msgid "Create and manage your company profile on workloupe.com"
msgstr "Twórz i zarządzaj profilem swojej firmy na workloupe.com"

#: templates/workloupe_platform.html:18
msgid ""
"Update your existing profile information below. All changes will be saved "
"automatically."
msgstr ""
"Zaktualizuj swoje istniejące informacje profilowe poniżej. Wszystkie zmiany "
"zostaną zapisane automatycznie."

#: templates/workloupe_platform.html:36
msgid "Company name is required"
msgstr "Nazwa firmy jest wymagana"

#: templates/workloupe_platform.html:41
msgid "Company Email"
msgstr "E-mail firmy"

#: templates/workloupe_platform.html:43
msgid "Please enter a valid email address"
msgstr "Proszę podać prawidłowy adres e-mail"

#: templates/workloupe_platform.html:51
msgid "Phone Number"
msgstr "Numer telefonu"

#: templates/workloupe_platform.html:53
msgid "Include country code (e.g., ******-123-4567)"
msgstr "Uwzględnij kod kraju (np. +48 ***********)"

#: templates/workloupe_platform.html:58
msgid "Website"
msgstr "Strona internetowa"

#: templates/workloupe_platform.html:60
msgid "Please enter a valid website URL"
msgstr "Proszę podać prawidłowy adres URL strony"

#: templates/workloupe_platform.html:66
msgid "Company Address"
msgstr "Adres firmy"

#: templates/workloupe_platform.html:67
msgid "Full company address including city, state, country"
msgstr "Pełny adres firmy, w tym miasto, województwo, kraj"

#: templates/workloupe_platform.html:71
msgid "Office Locations"
msgstr "Lokalizacje biur"

#: templates/workloupe_platform.html:72
msgid "Separate multiple locations with | (e.g., New York | London | Remote)"
msgstr ""
"Oddziel wiele lokalizacji znakiem | (np. Warszawa | Kraków | Praca zdalna)"

#: templates/workloupe_platform.html:73
msgid "Use | to separate multiple office locations"
msgstr "Użyj | do oddzielenia wielu lokalizacji biur"

#: templates/workloupe_platform.html:77
msgid "Company Description"
msgstr "Opis firmy"

#: templates/workloupe_platform.html:78
msgid ""
"Describe your company, mission, values, and what makes it special. This will "
"be prominently displayed on your profile."
msgstr ""
"Opisz swoją firmę, misję, wartości i to, co czyni ją wyjątkową. Będzie to "
"widoczne w Twoim profilu."

#: templates/workloupe_platform.html:79
msgid "Company description is required"
msgstr "Opis firmy jest wymagany"

#: templates/workloupe_platform.html:80
msgid "Minimum 50 characters recommended for better visibility"
msgstr "Zalecane minimum 50 znaków dla lepszej widoczności"

#: templates/workloupe_platform.html:88
msgid "Company Details"
msgstr "Szczegóły firmy"

#: templates/workloupe_platform.html:94
msgid "Industry"
msgstr "Branża"

#: templates/workloupe_platform.html:96
msgid "Select Industry"
msgstr "Wybierz branżę"

#: templates/workloupe_platform.html:97
msgid "Technology"
msgstr "Technologia"

#: templates/workloupe_platform.html:98
msgid "Healthcare"
msgstr "Opieka zdrowotna"

#: templates/workloupe_platform.html:99
msgid "Finance"
msgstr "Finanse"

#: templates/workloupe_platform.html:100
msgid "Education"
msgstr "Edukacja"

#: templates/workloupe_platform.html:101
msgid "Manufacturing"
msgstr "Produkcja"

#: templates/workloupe_platform.html:102
msgid "Retail"
msgstr "Handel detaliczny"

#: templates/workloupe_platform.html:103
msgid "Consulting"
msgstr "Konsulting"

#: templates/workloupe_platform.html:104
msgid "Marketing & Advertising"
msgstr "Marketing i reklama"

#: templates/workloupe_platform.html:105
msgid "Real Estate"
msgstr "Nieruchomości"

#: templates/workloupe_platform.html:106
msgid "Non-profit"
msgstr "Non-profit"

#: templates/workloupe_platform.html:107
msgid "Other"
msgstr ""

#: templates/workloupe_platform.html:113
msgid "Company Size"
msgstr "Wielkość firmy"

#: templates/workloupe_platform.html:114
msgid "Number of employees"
msgstr "Liczba pracowników"

#: templates/workloupe_platform.html:115
msgid "Please enter a valid number of employees"
msgstr "Proszę podać prawidłową liczbę pracowników"

#: templates/workloupe_platform.html:116
msgid "Enter the total number of employees in your company"
msgstr "Wpisz całkowitą liczbę pracowników w Twojej firmie"

#: templates/workloupe_platform.html:122
msgid "Social Media Links"
msgstr "Linki do mediów społecznościowych"

#: templates/workloupe_platform.html:122
msgid "Optional"
msgstr "Opcjonalnie"

#: templates/workloupe_platform.html:128
msgid "Please enter a valid LinkedIn URL"
msgstr "Proszę podać prawidłowy adres URL LinkedIn"

#: templates/workloupe_platform.html:133
msgid "Please enter a valid Instagram URL"
msgstr "Proszę podać prawidłowy adres URL Instagram"

#: templates/workloupe_platform.html:140
msgid "Please enter a valid Twitter URL"
msgstr "Proszę podać prawidłowy adres URL Twitter"

#: templates/workloupe_platform.html:145
msgid "Please enter a valid Github URL"
msgstr "Proszę podać prawidłowy adres URL GitHub"

#: templates/workloupe_platform.html:152
msgid "Please enter a valid Facebook URL"
msgstr "Proszę podać prawidłowy adres URL Facebook"

#: templates/workloupe_platform.html:157
msgid "Please enter a valid Glassdoor URL"
msgstr "Proszę podać prawidłowy adres URL Glassdoor"

#: templates/workloupe_platform.html:161
msgid ""
"Add your company's social media profiles to increase visibility. URLs will "
"be validated automatically."
msgstr ""
"Dodaj profile mediów społecznościowych swojej firmy, aby zwiększyć "
"widoczność. Adresy URL zostaną automatycznie zweryfikowane."

#: templates/workloupe_platform.html:169
msgid "Branding Assets"
msgstr "Elementy brandingowe"

#: templates/workloupe_platform.html:169
msgid "(Publicly Visible)"
msgstr "(Publicznie widoczne)"

#: templates/workloupe_platform.html:177
msgid "Recommended: 300x300px, PNG or JPG. Max 3MB."
msgstr "Rekomendowane: 300x300px, PNG lub JPG. Maks. 3MB."

#: templates/workloupe_platform.html:181
msgid "Current logo - upload new file to replace"
msgstr "Bieżące logo - prześlij nowy plik, aby zastąpić"

#: templates/workloupe_platform.html:185 templates/workloupe_platform.html:1165
msgid "No logo uploaded"
msgstr "Brak przesłanego logo"

#: templates/workloupe_platform.html:193
msgid "Company Banner"
msgstr "Baner firmy"

#: templates/workloupe_platform.html:195
msgid "Recommended: 1200x400px, PNG or JPG. Max 3MB."
msgstr "Rekomendowane: 1200x400px, PNG lub JPG. Maks. 3MB."

#: templates/workloupe_platform.html:199
msgid "Current banner - upload new file to replace"
msgstr "Bieżący baner - prześlij nowy plik, aby zastąpić"

#: templates/workloupe_platform.html:203 templates/workloupe_platform.html:1172
msgid "No banner uploaded"
msgstr "Brak przesłanego banera"

#: templates/workloupe_platform.html:213
msgid ""
"If you don't have online versions of your logo or banner, you can upload "
"them here. They will be stored securely and made publicly accessible for "
"your profile."
msgstr ""
"Jeśli nie masz wersji online swojego logo lub banera, możesz przesłać je "
"tutaj. Będą przechowywane bezpiecznie i udostępnione publicznie w Twoim "
"profilu."

#: templates/workloupe_platform.html:221
msgid "Company Gallery"
msgstr "Galeria firmy"

#: templates/workloupe_platform.html:226
msgid "Important:"
msgstr "Ważne:"

#: templates/workloupe_platform.html:226
msgid ""
"Photos will be public once you publish your profile. Maximum 50 photos "
"allowed. We reserve the right to remove inappropriate content. Only upload "
"professional, work-appropriate images."
msgstr ""
"Zdjęcia będą publiczne po opublikowaniu profilu. Maksymalnie 50 zdjęć. "
"Zastrzegamy sobie prawo do usuwania nieodpowiednich treści. Przesyłaj tylko "
"profesjonalne, odpowiednie do pracy zdjęcia."

#: templates/workloupe_platform.html:232
msgid "Current Photos"
msgstr "Bieżące zdjęcia"

#: templates/workloupe_platform.html:238
msgid "Loading existing photos..."
msgstr "Ładowanie istniejących zdjęć..."

#: templates/workloupe_platform.html:246
msgid "Upload New Photos"
msgstr "Prześlij nowe zdjęcia"

#: templates/workloupe_platform.html:249
msgid ""
"Select multiple photos (JPG, PNG, WebP). Max 5MB per photo. Showcase your "
"company culture, office, team, events, etc."
msgstr ""
"Wybierz wiele zdjęć (JPG, PNG, WebP). Maks. 5MB na zdjęcie. Pokaż kulturę "
"swojej firmy, biuro, zespół, wydarzenia itp."

#: templates/workloupe_platform.html:260
msgid "Uploading photos..."
msgstr "Przesyłanie zdjęć..."

#: templates/workloupe_platform.html:268
msgid "Reset Form"
msgstr "Resetuj formularz"

#: templates/workloupe_platform.html:272
msgid "Save & Publish Profile"
msgstr "Zapisz i opublikuj profil"

#: templates/workloupe_platform.html:279
msgid ""
"Your data is secure and will only be used for your public company profile"
msgstr ""
"Twoje dane są bezpieczne i będą używane tylko do publicznego profilu firmy"

#: templates/workloupe_platform.html:295
msgid "Profile Published Successfully!"
msgstr "Profil opublikowany pomyślnie!"

#: templates/workloupe_platform.html:302
msgid "Your company is now live on Workloupe!"
msgstr "Twoja firma jest teraz aktywna na Workloupe!"

#: templates/workloupe_platform.html:303
msgid ""
"Your company profile has been created and published on workloupe.com. "
"Candidates can now discover your company and apply to your jobs."
msgstr ""
"Twój profil firmy został utworzony i opublikowany na workloupe.com. "
"Kandydaci mogą teraz odkryć Twoją firmę i aplikować na Twoje oferty pracy."

#: templates/workloupe_platform.html:307
msgid "Your Profile URL:"
msgstr "URL Twojego profilu:"

#: templates/workloupe_platform.html:321
msgid "Share Your Profile"
msgstr "Udostępnij swój profil"

#: templates/workloupe_platform.html:322
msgid "Share this link with candidates and on social media"
msgstr "Udostępnij ten link kandydatom i w mediach społecznościowych"

#: templates/workloupe_platform.html:330
msgid "Update Anytime"
msgstr "Aktualizuj w dowolnym momencie"

#: templates/workloupe_platform.html:331
msgid "Return to this page to update your profile information"
msgstr "Wróć na tę stronę, aby zaktualizować informacje o profilu"

#: templates/workloupe_platform.html:341
msgid "View Live Profile"
msgstr "Zobacz aktywny profil"

#: templates/workloupe_platform.html:825
msgid "New logo selected - will be uploaded on save"
msgstr "Nowe logo wybrane - zostanie przesłane przy zapisie"

#: templates/workloupe_platform.html:842
msgid "New banner selected - will be uploaded on save"
msgstr "Nowy baner wybrany - zostanie przesłany przy zapisie"

#: templates/workloupe_platform.html:850
msgid "Please select a valid image file (JPG, PNG, WebP)"
msgstr "Proszę wybrać prawidłowy plik obrazu (JPG, PNG, WebP)"

#: templates/workloupe_platform.html:856
msgid "File size must be less than"
msgstr "Rozmiar pliku musi być mniejszy niż"

#: templates/workloupe_platform.html:868
msgid "No existing photos found"
msgstr "Nie znaleziono istniejących zdjęć"

#: templates/workloupe_platform.html:889
msgid "Remove image"
msgstr "Usuń obraz"

#: templates/workloupe_platform.html:919
msgid "Failed to remove image. Please try again."
msgstr "Nie udało się usunąć obrazu. Proszę spróbować ponownie."

#: templates/workloupe_platform.html:924
msgid "An error occurred while removing the image."
msgstr "Wystąpił błąd podczas usuwania obrazu."

#: templates/workloupe_platform.html:953
msgid "You can only upload"
msgstr "Możesz przesłać tylko"

#: templates/workloupe_platform.html:953
msgid "more photos. Maximum"
msgstr "więcej zdjęć. Maksymalnie"

#: templates/workloupe_platform.html:953
msgid "photos allowed."
msgstr "zdjęć dozwolonych."

#: templates/workloupe_platform.html:1042
msgid "Please fix the validation errors before submitting."
msgstr "Proszę poprawić błędy walidacji przed wysłaniem."

#: templates/workloupe_platform.html:1097
msgid "Publishing..."
msgstr "Publikowanie..."

#: templates/workloupe_platform.html:1119
msgid "Unknown error occurred"
msgstr "Wystąpił nieznany błąd"

#: templates/workloupe_platform.html:1124
msgid "An error occurred while publishing your profile. Please try again."
msgstr "Wystąpił błąd podczas publikowania profilu. Proszę spróbować ponownie."

#: templates/workloupe_platform.html:1153
msgid "Failed to copy URL. Please copy it manually."
msgstr "Nie udało się skopiować URL. Skopiuj go ręcznie."

#: templates/workloupe_platform.html:1158
msgid ""
"Are you sure you want to reset the form? All unsaved changes will be lost."
msgstr ""
"Czy na pewno chcesz zresetować formularz? Wszystkie niezapisane zmiany "
"zostaną utracone."
